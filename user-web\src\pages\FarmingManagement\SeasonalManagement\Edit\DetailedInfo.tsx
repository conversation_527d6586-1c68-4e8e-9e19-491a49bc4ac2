import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import {
  ProForm,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Card } from 'antd';
import { FC, ReactNode } from 'react';

interface DetailedInfoProps {
  children?: ReactNode;
}

const DetailedInfo: FC<DetailedInfoProps> = ({ children }) => {
  return (
    <Card title="Thông tin chi tiết">
      <ProForm.Group>
        <ProFormText
          label={'Tên công việc'}
          rules={[
            {
              required: true,
            },
          ]}
          width={'lg'}
        />
        <ProFormSelect
          label="Chọn dự án"
          rules={[
            {
              required: true,
            },
          ]}
          width={'lg'}
        />
        <ProFormSelect
          label="Chọn khu vực"
          rules={[
            {
              required: true,
            },
          ]}
          width={'lg'}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSelect
          label="Chọn vụ mùa"
          rules={[
            {
              required: true,
            },
          ]}
          width={'lg'}
        />
        <ProFormSelect
          label="Loại giai đoạn mẫu"
          rules={[
            {
              required: true,
            },
          ]}
          width={'lg'}
        />
        <ProFormDateRangePicker
          label="Thời gian hoàn thành"
          width={'lg'}
          fieldProps={{
            format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
          }}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormSelect label="Người thực hiện" width={'lg'} />
        <ProFormSelect label="Thành viên liên quan" width={'lg'} />
        <ProFormText label="Ghi chú" width={'lg'} />
      </ProForm.Group>
    </Card>
  );
};

export default DetailedInfo;
