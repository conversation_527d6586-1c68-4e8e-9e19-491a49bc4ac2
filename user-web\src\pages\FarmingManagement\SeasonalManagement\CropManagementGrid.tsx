import FallbackComponent from '@/components/FallbackContent';
import TabsWithSearch from '@/components/TabsWithSearch';
import { getCropManagementInfoList, ICropManagerInfo } from '@/services/cropManager';
import { toLowerCaseNonAccentVietnamese } from '@/services/utils';
import { TableSkeleton } from '@ant-design/pro-components';
import { Access, useAccess, useIntl, useRequest } from '@umijs/max';
import { Card, Col, DatePicker, List, Space } from 'antd';
import { debounce } from 'lodash';
import moment from 'moment';
import { FC, ReactNode, Suspense, useCallback, useEffect, useState } from 'react';
import CropByPlant from '../Plant';
import CropByZone from '../Zone';
import Filter from './components/Filter';
import GeneralSeasonalCard from './components/GeneralSeasonalCard';

const { RangePicker } = DatePicker;

interface WorkflowManagementProps {
  children?: ReactNode;
}

const CropManagementGrid: FC<WorkflowManagementProps> = ({ children }) => {
  const [firstLoad, setFirstLoad] = useState(true);
  const [filteredCrop, setFilteredCrop] = useState<ICropManagerInfo[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [queryParams, setQueryParams] = useState<any>({ order_by: 'crop.creation desc' });
  const intl = useIntl();

  const {
    data: cropList,
    loading,
    refresh,
  } = useRequest(() => getCropManagementInfoList(queryParams), {
    onSuccess: () => {
      setFirstLoad(false);
    },
    onError() {
      setFirstLoad(false);
    },
    refreshDeps: [queryParams],
  });

  useEffect(() => {
    if (cropList) {
      const filtered = cropList.filter((obj) =>
        toLowerCaseNonAccentVietnamese(
          obj.label || obj.plant_name || obj.zone_name || obj.project_name,
        ).includes(toLowerCaseNonAccentVietnamese(searchQuery)),
      );
      setFilteredCrop(filtered);
    }
  }, [cropList, searchQuery]);

  const debounceSearch = useCallback(
    debounce((query) => {
      setSearchQuery(query);
    }, 400),
    [],
  );

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    debounceSearch(query);
  };

  const debounceSearchDate = useCallback(
    debounce((query) => {
      setQueryParams((prev: any) => ({ ...prev, ...query, order_by: 'label desc' }));
    }, 400),
    [],
  );

  const handleSearchDate = (e: any) => {
    try {
      const start_date = e ? moment(e[0].$d).format('YYYY-MM-DD HH:mm:ss') : '';
      const end_date = e ? moment(e[1].$d).format('YYYY-MM-DD HH:mm:ss') : '';
      debounceSearchDate({ start_date, end_date });
    } catch (error) {
      // Handle error if necessary
    }
  };

  const onPlantChange = (value: string) => {
    debounceSearchDate({ plant_id: value });
  };

  const onZoneChange = (value: string) => {
    debounceSearchDate({ zone_id: value });
  };

  const access = useAccess();
  const canCreateCrop = access.canCreateInSeasonalManagement();

  return (
    <Access accessible={access.canAccessPageSeasonalManagement()} fallback={<FallbackComponent />}>
      <Col>
        <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
          <Filter
            canCreateCrop={canCreateCrop}
            handleSearchDate={handleSearchDate}
            handleSearch={handleSearch}
            onPlantChange={onPlantChange}
            onZoneChange={onZoneChange}
          />
          <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
            <Card>
              <TabsWithSearch
                tabsItems={[
                  {
                    label: intl.formatMessage({ id: 'seasonalTab.ongoing' }),
                    tabKey: 'ongoing',
                    fallback: <TableSkeleton active />,
                    component() {
                      return (
                        <List
                          grid={{ column: 3, gutter: 10 }}
                          dataSource={
                            filteredCrop.length > 0
                              ? filteredCrop.filter(
                                  (d) => d.status === 'In progress' && d.is_template == false,
                                )
                              : cropList?.filter(
                                  (d) => d.status === 'In progress' && d.is_template == false,
                                )
                          }
                          renderItem={(item: any) => (
                            <List.Item>
                              <GeneralSeasonalCard {...item} />
                            </List.Item>
                          )}
                        />
                      );
                    },
                  },
                  {
                    label: intl.formatMessage({ id: 'seasonalTab.completed' }),
                    tabKey: 'completed',
                    fallback: <TableSkeleton active />,
                    component() {
                      return (
                        <List
                          grid={{ column: 3, gutter: 10 }}
                          dataSource={
                            filteredCrop.length > 0
                              ? filteredCrop.filter(
                                  (d) => d.status === 'Done' && d.is_template == false,
                                )
                              : cropList?.filter(
                                  (d) => d.status === 'Done' && d.is_template == false,
                                )
                          }
                          renderItem={(item: any) => (
                            <List.Item>
                              <GeneralSeasonalCard {...item} />
                            </List.Item>
                          )}
                        />
                      );
                    },
                  },
                  {
                    label: intl.formatMessage({ id: 'seasonalTab.all' }),
                    tabKey: 'all',
                    fallback: <TableSkeleton active />,
                    component() {
                      return (
                        <Suspense fallback={<TableSkeleton active />}>
                          <List
                            grid={{ column: 3, gutter: 10 }}
                            dataSource={
                              filteredCrop.length > 0
                                ? filteredCrop.filter((d) => d.is_template == false)
                                : cropList?.filter((d) => d.is_template == false)
                            }
                            renderItem={(item: any) => (
                              <List.Item>
                                <GeneralSeasonalCard {...item} />
                              </List.Item>
                            )}
                          />
                        </Suspense>
                      );
                    },
                  },
                  {
                    label: intl.formatMessage({ id: 'common.template-crop' }),
                    tabKey: 'template-crop',
                    fallback: <TableSkeleton active />,
                    component() {
                      return (
                        <Suspense fallback={<TableSkeleton active />}>
                          <List
                            grid={{ column: 3, gutter: 10 }}
                            dataSource={
                              filteredCrop.length > 0
                                ? filteredCrop.filter((d) => d.is_template == true)
                                : cropList?.filter((d) => d.is_template == true)
                            }
                            renderItem={(item: any) => (
                              <List.Item>
                                <GeneralSeasonalCard {...item} />
                              </List.Item>
                            )}
                          />
                        </Suspense>
                      );
                    },
                  },
                  {
                    label: intl.formatMessage({ id: 'common.zone' }),
                    tabKey: 'zone',
                    component: () => <CropByZone />,
                    fallback: <TableSkeleton active />,
                  },
                  {
                    label: intl.formatMessage({ id: 'common.plant' }),
                    tabKey: 'plant',
                    component: () => <CropByPlant />,
                    fallback: <TableSkeleton active />,
                  },
                ]}
              />
            </Card>
          </Space>
        </Space>
      </Col>
    </Access>
  );
};

export default CropManagementGrid;
