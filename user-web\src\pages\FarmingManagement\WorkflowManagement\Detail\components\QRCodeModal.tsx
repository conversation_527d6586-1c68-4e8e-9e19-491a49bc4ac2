import { ProFormInstance } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Form, Modal, QRCode, Typography } from 'antd';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { useState } from 'react';
const { Item } = Form;
const { Text } = Typography;

const handleDownload = () => {
  const doc = new jsPDF({
    unit: 'px',
    format: [400, 600],
  });

  const contentElement = document.getElementById('myqrcode');
  if (contentElement) {
    html2canvas(contentElement).then((canvas) => {
      const imgData = canvas.toDataURL('image/png');
      doc.addImage(imgData, 'PNG', 0, 0, 400, 600);
      doc.save('task_qr_code.pdf');
    });
  }
};

const QRCodeModal = ({ form, taskId }: { form?: ProFormInstance; taskId?: string }) => {
  const [isOpen, setOpen] = useState(false);

  const showModal = () => {
    setOpen(true);
  };

  const hideModal = () => {
    setOpen(false);
  };

  const handleOk = () => {
    handleDownload();
  };

  const handleCancel = () => {
    hideModal();
  };
  const intl = useIntl();
  return (
    <>
      <div onClick={showModal}>
        <QRCode size={100} value={`task,${taskId}`} bgColor="#fff" />
        {/* <span>
          <FormattedMessage id="common.download_qr_code" />
        </span> */}
      </div>
      {/* <a onClick={showModal}>
        <FormattedMessage id="common.download_qr_code" />
      </a> */}

      <Modal
        title={intl.formatMessage({ id: 'common.qr_code_for_task' })}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText={'Tải xuống'}
        cancelText={'Đóng'}
      >
        <div
          id="myqrcode"
          style={{
            width: '400px',
            height: '400px',
            position: 'relative',
            margin: 'auto',
          }}
        >
          <QRCode size={400} value={`task,${taskId}`} bgColor="#fff" style={{ paddingBlock: 20 }} />
          {/* <Text style={{ fontSize: 20 }}>
              Tên công việc: <strong>Fuck</strong>
            </Text> */}
        </div>
      </Modal>
    </>
  );
};

export default QRCodeModal;
