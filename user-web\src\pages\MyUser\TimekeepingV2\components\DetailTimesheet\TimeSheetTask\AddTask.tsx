import { DEFAULT_PAGE_SIZE_ALL, DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getCropManagementInfoList } from '@/services/cropManager';
import {
  getFarmingPlanList,
  getFarmingPlanState,
  getTaskManagerList,
} from '@/services/farming-plan';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-components';
import { Button } from 'antd';
import { FC } from 'react';

import withTriggerFormModal, { TriggerFormModalProps } from '@/HOC/withTriggerFormModal';
import { ModalForm } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';

type AddNewZoneInProjectProps = TriggerFormModalProps<{
  defaultValue?: any;
  // timesheet_id: string;
  onSelectTask?: (task: {
    label?: string;
    start_date: string;
    end_date: string;
    completion_percentage: number;
  }) => void;
}>;
const space = '########++++++!!!!!%%%';
const ContentForm: FC<AddNewZoneInProjectProps> = ({
  open,
  onOpenChange,
  onSuccess,
  modalProps,
  trigger,
}) => {
  // const { formatMessage } = useIntl();
  const [form] = ProForm.useForm();
  // const { message } = App.useApp();

  return (
    <ModalForm
      title="Add task"
      name="add:task"
      form={form}
      width={500}
      onFinish={async (values) => {
        // if (!values.task) {
        //   message.error(`Task is required`);
        //   return false;
        // }
        // const [start_date, end_date] = values.rangeDate;
        modalProps?.onSelectTask?.({
          label: !values.isSelectTask ? values.label : (values.task as string)?.split(space)?.[1],
          start_date: '',
          end_date: '',
          completion_percentage: values.completion_percentage,
        });
        return true;
      }}
      open={open}
      onOpenChange={onOpenChange}
      trigger={trigger}
      initialValues={{
        isSelectTask: true,
        completion_percentage: 0,
      }}
    >
      {/* <ProFormDateRangePicker
        label="Range date"
        name="rangeDate"
        rules={[
          {
            required: true,
          },
        ]}
      /> */}
      <ProFormDigit
        min={0}
        max={100}
        rules={[
          {
            required: true,
          },
        ]}
        label="Tiến độ hoàn thành"
        name="completion_percentage"
      />
      <ProFormSwitch name="isSelectTask" label="Chọn công việc từ vụ mùa" />
      <ProFormDependency name={['isSelectTask']}>
        {({ isSelectTask }) => {
          if (!isSelectTask) {
            return (
              <ProFormText
                rules={[
                  {
                    required: true,
                  },
                ]}
                label="Tên"
                name="label"
              />
            );
          }
          return (
            <>
              <ProFormSelect
                showSearch
                label="Chọn vụ mùa"
                rules={[
                  {
                    required: true,
                  },
                ]}
                onChange={() => {
                  form.setFieldsValue({
                    farming_plan: undefined,
                    farming_plan_state: undefined,
                    task: undefined,
                  });
                }}
                request={async () => {
                  const res = await getCropManagementInfoList({
                    page: 1,
                    size: DEFAULT_PAGE_SIZE_ALL,
                  });
                  return res.data.map((item: any) => ({
                    label: item.label,
                    value: item.name,
                  }));
                }}
                name="crop"
                colProps={{
                  md: 8,
                  sm: 24,
                }}
              />
              <ProFormDependency name={['crop']}>
                {({ crop }) => (
                  <ProFormSelect
                    showSearch
                    dependencies={['crop']}
                    label="Chọn kế hoạch"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    onChange={(v: any) => {
                      form.setFieldsValue({
                        //  farming_plan: undefined,
                        farming_plan_state: undefined,
                        task: undefined,
                      });
                    }}
                    disabled={!crop}
                    request={async () => {
                      if (!crop) return [];
                      const res: any = await getFarmingPlanList({
                        page: 1,
                        size: DEFAULT_PAGE_SIZE_ALL,
                        filters: [[DOCTYPE_ERP.iotFarmingPlan, 'crop', '=', crop]],
                      });
                      return res.data.map((item: any) => ({
                        label: item.label,
                        value: item.name,
                      }));
                    }}
                    name="farming_plan"
                  />
                )}
              </ProFormDependency>

              <ProFormDependency name={['farming_plan']} ignoreFormListField>
                {({ farming_plan, crop, ...rest }) => (
                  <ProFormSelect
                    showSearch
                    dependencies={['farming_plan']}
                    label="Chọn giai đoạn"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    disabled={!farming_plan}
                    onChange={(v: any) => {
                      form.setFieldsValue({
                        //  farming_plan: undefined,
                        // farming_plan_state: undefined,
                        task: undefined,
                      });
                    }}
                    name="farming_plan_state"
                    request={async () => {
                      if (!farming_plan) return [];
                      const res: any = await getFarmingPlanState({
                        page: 1,
                        size: DEFAULT_PAGE_SIZE_ALL,
                        filters: [
                          [DOCTYPE_ERP.iotFarmingPlanState, 'farming_plan', '=', farming_plan],
                        ],
                      });
                      return res.data.map((item: any) => ({
                        label: item.label,
                        value: item.name,
                      }));
                    }}
                  />
                )}
              </ProFormDependency>
              <ProFormDependency name={['farming_plan_state']}>
                {({ farming_plan_state }) => (
                  <ProFormSelect
                    showSearch
                    dependencies={['farming_plan_state']}
                    label="Chọn công việc"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    disabled={!farming_plan_state}
                    name="task"
                    request={async () => {
                      if (!farming_plan_state) return [];
                      const res: any = await getTaskManagerList({
                        page: 1,
                        size: DEFAULT_PAGE_SIZE_ALL,
                        filters: [
                          [
                            DOCTYPE_ERP.iotFarmingPlanTask,
                            'farming_plan_state',
                            'like',
                            farming_plan_state,
                          ],
                        ],
                      });
                      return res.data.map((item: any) => ({
                        label: item.label,
                        value: `${item.name}${space}${item.label}`, // trick
                      }));
                    }}
                  />
                )}
              </ProFormDependency>
            </>
          );
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

const AddNewZone = withTriggerFormModal({
  defaultTrigger: ({ changeOpen, disabled }) => (
    <Button
      disabled={disabled}
      type="primary"
      icon={<PlusOutlined />}
      onClick={() => changeOpen(true)}
    >
      <FormattedMessage id="common.add_new_task" />
    </Button>
  ),
  contentRender: ContentForm,
});
export default AddNewZone;
