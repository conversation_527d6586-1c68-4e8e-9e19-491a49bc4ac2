import {
  DEFAULT_DATE_FORMAT_WITHOUT_TIME,
  DEFAULT_PAGE_SIZE_ALL,
} from '@/common/contanst/constanst';
import { IUpdateCropReq, updateCrop } from '@/services/cropManager';
import { getPlantUserOwnerAllResources } from '@/services/plantRefAndUserOwner';
import { zoneList } from '@/services/zones';
import { CameraFilled, DeleteOutlined } from '@ant-design/icons';
import {
  ProFormDateRangePicker,
  ProFormDigit,
  ProFormGroup,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { history, useAccess, useIntl } from '@umijs/max';
import { Button, Card, Col, Form, message, Row, Space } from 'antd';
import modal from 'antd/es/modal';
import { FC, ReactNode, useRef } from 'react';
import QRCodeModal from './components/QRCodeModal';

interface DetailedInfoProps {
  children?: ReactNode;
  cropId: string;
  form: ProFormInstance;
}

const DetailedInfo: FC<DetailedInfoProps> = ({ children, cropId }) => {
  const componentRef = useRef(null);

  const intl = useIntl();
  const access = useAccess();
  const canDelete = access.canDeleteInSeasonalManagement();
  const form = Form.useFormInstance();
  const onDeleteCrop = () => {
    modal.confirm({
      content: 'Bạn có chắc chắn muốn xoá vụ mùa này?',
      onOk: async () => {
        try {
          // await deletePlan({
          //   name: planId,
          // });
          const updateBody: IUpdateCropReq = { name: cropId, is_deleted: 1 };
          await updateCrop(updateBody);
          message.success({
            content: 'Delete successfully',
          });

          history.push(`/farming-management/seasonal-management`);

          // await refresh();
          return true;
        } catch (error) {
          message.error({
            content: 'Delete error, please try again',
          });
          return false;
        }
      },
      okButtonProps: {
        danger: true,
      },
    });
  };
  return (
    <Card
      title={intl.formatMessage({ id: 'seasonalTab.detailInformation' })}
      extra={
        <>
          {canDelete && (
            <Button
              size="small"
              icon={<DeleteOutlined />}
              key={'delete'}
              danger
              onClick={onDeleteCrop}
            >
              {intl.formatMessage({ id: 'common.delete' })}
            </Button>
          )}
        </>
      }
    >
      <Row gutter={10}>
        <Col md={3} sm={24}>
          <Space align="center" direction="vertical" size={'small'}>
            <ProFormUploadButton
              label={intl.formatMessage({ id: 'common.form.image' })}
              accept="image/*"
              listType="picture-card"
              icon={<CameraFilled />}
              title=""
              name="avatar"
              max={1} // Set maximum files to 1
              style={{
                width: 80,
              }}
            />
            {/* <Button type="link"> */}
            <QRCodeModal form={form} cropId={cropId} />
            {/* <QRCode
                size={50}
                value={`crop,${cropId}`}
                bgColor="#fff"
                style={{ marginBottom: 16 }}
              /> */}
            {/* </Button> */}
          </Space>
        </Col>
        <Col md={14} sm={24}>
          <ProFormGroup>
            <ProFormText
              label={intl.formatMessage({ id: 'seasonalTab.seasonName' })}
              rules={[
                {
                  required: true,
                },
              ]}
              name="label"
              colProps={{ span: 12 }}
            />
            <ProFormSelect
              label={intl.formatMessage({ id: 'common.area' })}
              rules={[
                {
                  required: true,
                },
              ]}
              request={async () => {
                const res = await zoneList({ size: Number.MAX_SAFE_INTEGER, page: 1 });
                return res.data.map((item) => ({
                  label: item.label,
                  value: item.name,
                }));
              }}
              name="zone_id"
              // disabled
              colProps={{ span: 12 }}
            />
          </ProFormGroup>
          <ProFormGroup>
            <ProFormDateRangePicker
              fieldProps={{
                format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
              }}
              label={intl.formatMessage({ id: 'seasonalTab.time_completed' })}
              rules={[
                {
                  required: true,
                },
              ]}
              name="date_range"
              colProps={{ span: 12 }}
            />
            <ProFormDigit
              label={intl.formatMessage({ id: 'seasonalTab.cultivation_area' })}
              min={0}
              name="square"
              colProps={{ span: 12 }}
            />
          </ProFormGroup>
          <ProFormGroup>
            <ProFormSelect
              label={intl.formatMessage({ id: 'seasonalTab.selectTypeOfPlant' })}
              request={async () => {
                const res = await getPlantUserOwnerAllResources({
                  size: DEFAULT_PAGE_SIZE_ALL,
                });

                return res.data.map((item) => ({
                  label: item.label,
                  value: item.name,
                }));
              }}
              name="plant_id"
              colProps={{ span: 12 }}
            />
            <ProFormDigit
              label={intl.formatMessage({ id: 'seasonalTab.expectedOutputInKg' })}
              min={0}
              name="quantity_estimate"
              colProps={{ span: 12 }}
            />
          </ProFormGroup>
        </Col>
        <Col md={7} sm={24}>
          <ProFormSelect
            rules={[
              {
                required: true,
              },
            ]}
            label={intl.formatMessage({ id: 'common.status' })}
            name="status"
            options={[
              {
                label: 'Đang diễn ra',
                value: 'In progress',
              },
              {
                label: 'Hoàn tất',
                value: 'Done',
              },
            ]}
            initialValue={'Plan'}
            colProps={{ span: 24 }}
          />
          <ProFormTextArea
            label={intl.formatMessage({ id: 'common.note' })}
            name="description"
            colProps={{ span: 24 }}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default DetailedInfo;
