import { DeleteOutlined } from '@ant-design/icons';
import { FormattedMessage, Link, useAccess } from '@umijs/max';
import { App, Avatar, Button, Card, Popconfirm, Space, Typography } from 'antd';
import Meta from 'antd/es/card/Meta';

import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import { deleteWarehouse } from '@/services/stock/warehouse';
import { IWarehouse } from '@/types/warehouse.type';
import { genDownloadUrl } from '@/utils/file';
import EditWarehouse from './EditWarehouse';

const { Text } = Typography;
const GeneralWarehouseCard = ({
  label,
  description,
  image,
  onDeleteSuccess,
  onEditSuccess,
  name,
}: IWarehouse & {
  onDeleteSuccess?: any;
  onEditSuccess?: any;
}) => {
  const { message } = App.useApp();
  async function handleDelete() {
    try {
      if (name) {
        await deleteWarehouse({ name, label });
        message.success(`Success in deleting warehouse ${label}`);
        onDeleteSuccess?.();
      }
    } catch (error) {
      message.error('Error when deleting warehouse.');
    }
  }
  function handleEdit(): void {
    message.error('Function not yet implemented.');
  }

  const access = useAccess();
  const canUpdateStorage = access.canUpdateInStorageNewManagement();
  const canDeleteStorage = access.canDeleteInStorageNewManagement();

  const canReadCategoryInventory = access.canAccessPageCategoryInventoryManagement();
  const canReadProductInventory = access.canAccessPageProductInventoryManagement();

  return (
    <Card
      size="default"
      // style={{ maxHeight: 250 }}
      hoverable
      actions={[
        <Space key={'edit'} direction="vertical">
          <>
            {canUpdateStorage && (
              <EditWarehouse
                key={'edit'}
                data={{ label, description, image, name }}
                onSuccess={onEditSuccess}
              />
            )}
          </>
        </Space>,

        <Space key={'delete'}>
          <>
            {canDeleteStorage && (
              <Popconfirm
                title={
                  <FormattedMessage id={'warehouse-management.warehouse-list.delete-warehouse'} />
                }
                description={<FormattedMessage id={'action.verify'} />}
                onConfirm={() => handleDelete()}
                key="delete"
                onPopupClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  key="delete"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                ></Button>
              </Popconfirm>
            )}
          </>
        </Space>,
      ]}
    >
      <Link
        to="/warehouse-management-v3/inventory"
        onClick={() => {
          localStorage.setItem('selectedWarehouse', name);
        }}
      >
        <Meta
          style={{ minHeight: 100 }}
          avatar={
            <Avatar
              shape="square"
              size={54}
              src={image ? genDownloadUrl(image) : DEFAULT_FALLBACK_IMG}
            />
          }
          title={<Text style={{ whiteSpace: 'normal' }}>{label}</Text>}
          description={description}
        ></Meta>
      </Link>
    </Card>
  );
};

export default GeneralWarehouseCard;
