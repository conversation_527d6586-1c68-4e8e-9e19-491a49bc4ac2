import {
  DEFAULT_DATE_FORMAT_WITHOUT_TIME,
  DEFAULT_PAGE_SIZE_ALL,
  DOCTYPE_ERP,
} from '@/common/contanst/constanst';
import { getFarmingPlanFromTemplateCropList } from '@/services/farming-plan';
import { getPlantUserOwnerAllResources } from '@/services/plantRefAndUserOwner';
import { zoneList } from '@/services/zones';
import { CameraFilled } from '@ant-design/icons';
import {
  ProForm,
  ProFormCheckbox,
  ProFormDateRangePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Col, Divider, Row } from 'antd';
import { FC, ReactNode } from 'react';

interface DetailedInfoProps {
  children?: ReactNode;
}
const DetailedInfo: FC<DetailedInfoProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  return (
    <Card title={formatMessage({ id: 'common.detail' })}>
      <ProForm.Group>
        <ProFormUploadButton
          label={formatMessage({ id: 'common.avatar' })}
          accept="image/*"
          listType="picture-card"
          icon={<CameraFilled />}
          title=""
          name="avatar"
          max={1} // Set maximum files to 1
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormCheckbox name="is_template" label={formatMessage({ id: 'common.is_template' })} />
      </ProForm.Group>
      <Row gutter={24}>
        <Col span={12}>
          <ProFormText
            label={'Tên vụ mùa'}
            rules={[
              {
                required: true,
              },
            ]}
            name="label"
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            label="Chọn khu vực"
            rules={[
              {
                required: true,
              },
            ]}
            showSearch
            request={async () => {
              const res = await zoneList({ size: Number.MAX_SAFE_INTEGER, page: 1 });
              return res.data.map((item) => ({
                label: item.label,
                value: item.name,
              }));
            }}
            name="zone_id"
          />
        </Col>
        <Col span={12}>
          <ProFormDateRangePicker
            width={'xl'}
            label="Thời gian hoàn thành"
            rules={[
              {
                required: true,
              },
            ]}
            name="date_range"
            fieldProps={{
              format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
            }}
          />
        </Col>
        <Col span={12}>
          <ProFormDigit label="Diện tích canh tác (m2)" min={0} name="square" />
        </Col>
        <Col span={12}>
          <ProFormDigit label="Sản lượng dự kiến - Kg" min={0} name="quantity_estimate" />
        </Col>
        <Col span={12}>
          <ProFormSelect
            label="Chọn loại cây trồng"
            request={async () => {
              const res = await getPlantUserOwnerAllResources({
                size: DEFAULT_PAGE_SIZE_ALL,
              });
              return res.data.map((item) => ({
                label: item.label,
                value: item.name,
              }));
            }}
            name="plant_id"
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            rules={[
              {
                required: true,
              },
            ]}
            label="Trạng thái"
            name="status"
            options={[
              {
                label: 'Đang diễn ra',
                value: 'In progress',
              },
              {
                label: 'Hoàn tất',
                value: 'Done',
              },
            ]}
            initialValue={'In progress'}
          />
        </Col>
        <Col span={12}>
          <ProFormTextArea
            width={'xl'}
            name="description"
            label="Ghi chú"
            placeholder="Please enter a name"
          />
        </Col>
      </Row>
      <Divider type="horizontal" orientation="left" orientationMargin="0">
        {formatMessage({ id: 'common.copy-from-other-crop' })}
      </Divider>
      <Row>
        <Col span={12}>
          <ProFormSelect
            allowClear
            showSearch
            // label="Sao chép từ vụ mùa khác"
            name="copy_plan_id"
            request={async ({ keyWords }) => {
              const res = await getFarmingPlanFromTemplateCropList({
                page: 1,
                size: DEFAULT_PAGE_SIZE_ALL,
                ...(keyWords && {
                  filters: `[["${DOCTYPE_ERP.iotFarmingPlan}", "label", "like", "%${keyWords}%"]]`,
                }),
              });
              return res.data.map((item) => ({
                label: item.label,
                value: item.name,
              }));
            }}
          />
        </Col>
        {/* <ProFormText
          disabled
          // name="plan_label"
          name="label"
          label={'Tên kế hoạch'}
          rules={[
            {
              required: true,
            },
          ]}
        /> */}
        {/* <ProFormDateRangePicker
          colSize={24}
          disabled
          name="date_range"
          label="Thời gian"
          rules={[
            {
              required: true,
            },
          ]}
          fieldProps={{
            format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
          }}
        /> */}

        {/* <ProFormUploadButton
          max={1}
          accept="image/*"
          label="Hình ảnh / Video mô tả "
          listType="picture-card"
          icon={<CameraFilled />}
          title=""
          name="img"
        /> */}
      </Row>
    </Card>
  );
};

export default DetailedInfo;
