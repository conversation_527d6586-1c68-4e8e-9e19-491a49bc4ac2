import { PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, message, Modal, Row, Select } from 'antd';
import { FC, useState } from 'react';
const { Item } = Form;

import vietnam_location from '@/helpers/tree.json';
import { createCustomerUser, getDynamicRole } from '@/services/customerUser';
import { toLowerCaseNonAccentVietnamese } from '@/services/utils';
import { ProFormSelect } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import TextArea from 'antd/es/input/TextArea';

const { Option } = Select;
type CreateCustomerUserFormProps = {
  refreshFnc: any;
  customer_id: any;
  buttonType?: any;
};
const CreateCustomerUserForm: FC<CreateCustomerUserFormProps> = ({
  refreshFnc,
  customer_id,
  buttonType = 'primary',
}) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [stateOption, setStateOption] = useState([]);
  const [wardOption, setWardOption] = useState([]);

  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };
  const handleChangeCity = (value: any) => {
    if (value) {
      const new_state = Object.keys(vietnam_location[value]['quan-huyen']).map((key) => {
        return (
          <Option key={key} value={key}>
            {vietnam_location[value]['quan-huyen'][key].name_with_type}
          </Option>
        );
      });
      form.setFieldValue('district', null);
      form.setFieldValue('ward', null);
      setStateOption(new_state);
    } else {
      form.setFieldValue('district', null);
      form.setFieldValue('ward', null);
    }
  };

  const handleChangeState = (value: any) => {
    if (value) {
      const city = form.getFieldValue('province');
      if (city) {
        const new_ward: any = Object.keys(
          vietnam_location[city]['quan-huyen'][value]['xa-phuong'],
        ).map((key) => {
          return (
            <Option key={key} value={key}>
              {vietnam_location[city]['quan-huyen'][value]['xa-phuong'][key].name_with_type}
            </Option>
          );
        });
        form.setFieldValue('ward', null);
        setWardOption(new_ward);
      }
    } else {
      form.setFieldValue('ward', null);
    }
  };
  const { formatMessage } = useIntl();
  return (
    <>
      <Button
        type={buttonType}
        onClick={showModal}
        style={{ display: 'flex', alignItems: 'center' }}
      >
        <PlusOutlined />{' '}
        {formatMessage({
          id: 'common.add_new_user',
        })}
      </Button>
      <Modal
        title={formatMessage({
          id: 'common.add_new_user',
        })}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              let {
                email,
                phone,
                province,
                district,
                ward,
                address,
                description,
                first_name,
                last_name,
                password,
                iot_dynamic_role,
              } = value;
              let province_str,
                district_str,
                ward_str = '';
              province_str = vietnam_location[province]?.name_with_type || null;
              if (district)
                district_str = vietnam_location[province]['quan-huyen'][district]['name_with_type'];

              if (ward)
                ward_str =
                  vietnam_location[province]['quan-huyen'][district]['xa-phuong'][ward][
                    'name_with_type'
                  ];

              province = province_str;
              district = district_str;
              ward = ward_str;

              // await generalCreate('iot_customer_user', {
              //     data: {
              //         first_name,
              //         last_name,
              //         email,
              //         phone,
              //         province,
              //         district,
              //         ward,
              //         address,
              //         description,
              //         customer_id: customer_id
              //     }
              // });
              console.log('value is', value);

              await createCustomerUser({
                first_name,
                last_name,
                email,
                phone_number: phone,
                province,
                district,
                ward,
                address,
                description,
                customer_id: customer_id,
                password,
                iot_dynamic_role,
                is_deactivated: 0,
              });

              hideModal();
              if (refreshFnc) {
                await refreshFnc();
              }
              message.success('Success!');
            } catch (error: any) {
              // message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={16}>
            <Col className="gutter-row" md={6}>
              <Item
                label={formatMessage({
                  id: 'common.last_name',
                })}
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="last_name"
              >
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item
                label={formatMessage({
                  id: 'common.first_name',
                })}
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="first_name"
              >
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item
                label="Email"
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                  {
                    type: 'email',
                    message: 'Vui lòng nhập đúng định dạng email',
                  },
                ]}
                name="email"
              >
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item
                label="Phone"
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="phone"
              >
                <Input />
              </Item>
            </Col>
            <Col md={6}>
              <Item
                label={formatMessage({
                  id: 'common.password',
                })}
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="password"
              >
                <Input.Password />
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item label="Province" labelCol={{ span: 24 }} name="province">
                <Select
                  allowClear
                  showSearch
                  style={{
                    width: '100%',
                  }}
                  onChange={handleChangeCity}
                  filterOption={(input, option) =>
                    toLowerCaseNonAccentVietnamese(option!.children as unknown as string).includes(
                      toLowerCaseNonAccentVietnamese(input),
                    )
                  }
                >
                  {Object.keys(vietnam_location).map((key) => {
                    return (
                      <Option key={key} value={key}>
                        {vietnam_location[key].name}
                      </Option>
                    );
                  })}
                </Select>
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item label="District" labelCol={{ span: 24 }} name="district">
                <Select
                  allowClear
                  showSearch
                  style={{
                    width: '100%',
                  }}
                  onChange={handleChangeState}
                  filterOption={(input, option) =>
                    toLowerCaseNonAccentVietnamese(option!.children as unknown as string).includes(
                      toLowerCaseNonAccentVietnamese(input),
                    )
                  }
                >
                  {stateOption}
                </Select>
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item label="Ward" labelCol={{ span: 24 }} name="ward">
                <Select
                  allowClear
                  showSearch
                  style={{
                    width: '100%',
                  }}
                  filterOption={(input, option) =>
                    toLowerCaseNonAccentVietnamese(option!.children as unknown as string).includes(
                      toLowerCaseNonAccentVietnamese(input),
                    )
                  }
                >
                  {wardOption}
                </Select>
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item label="Address" labelCol={{ span: 24 }} name="address">
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item
                label={formatMessage({
                  id: 'common.role',
                })}
                required
                rules={[{ required: true, message: 'Vui lòng chọn vai trò' }]}
              >
                <ProFormSelect
                  name={'iot_dynamic_role'}
                  showSearch
                  rules={[{ required: true, message: 'Vui lòng chọn vai trò' }]}
                  request={async (option) => {
                    const roleList = await getDynamicRole();
                    console.log('roleList', roleList);
                    return roleList.map((item: any) => ({
                      label: item.label,
                      value: item.name,
                    }));
                  }}
                />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item label="Description" labelCol={{ span: 24 }} name="description">
                <TextArea rows={5} placeholder="maxLength is 100" maxLength={100} />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default CreateCustomerUserForm;
