import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import FallbackComponent from '@/components/FallbackContent';
import PageContainerTabsWithPath from '@/components/PageContainerTabsWithPath';
import { getWarehouseList } from '@/services/stock/warehouse';
import { toLowerCaseNonAccentVietnamese } from '@/services/utils';
import { IWarehouse } from '@/types/warehouse.type';
import { DownOutlined } from '@ant-design/icons';
import {
  DescriptionsSkeleton,
  ProFormDateRangePicker,
  ProFormSelect,
} from '@ant-design/pro-components';
import { Access, FormattedMessage, useAccess, useIntl } from '@umijs/max';
import { Button, Dropdown, Skeleton, Space } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useDateRangeStore } from '../hooks/useDateRangeStore';
import { useSelectedWarehousedStore } from '../hooks/useWarehouseStore';
// import ExportVoucher from './components/ExportVoucher';
import ExportVoucher from './components/ExportVoucherEnhanced';
// import ImportVoucher from './components/ImportVoucher';
import ImportVoucher from './components/ImportVoucherEnhanced';
import MaterialIssue from './components/MaterialIssueEnhanced';
import MaterialReceipt from './components/MaterialReceiptEnhanced';
import MaterialTransfer from './components/MaterialTransferEnhanced';
import StockReconciliation from './components/ReconciliationVoucherEnhanced';
import ExportHistory from './ExportHistory';
import ImportHistory from './ImportHistory';
import InventoryListTable from './InventoryListTable';
import ReconciliationHistory from './ReconciliationHistory';
import WarehouseReport from './Report';
import StockEntryHistory from './StockEntryHistory';

const Inventory = () => {
  const [warehouses, setWarehouses] = useState<IWarehouse[]>([]);
  const { selectedWarehouse, setSelectedWarehouse } = useSelectedWarehousedStore();
  const { dateRange, setDateRange } = useDateRangeStore();
  const [refreshIndicator, setRefreshIndicator] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const access = useAccess();
  const readInventoryValue = access.canReadCategoryInventoryFieldLevelManagement();
  const canCreateTransaction = access.canCreateInCategoryInventoryManagement();
  const handleRefresh = () => {
    setRefreshIndicator((prev) => prev + 1);
  };
  const fetchData = async () => {
    const data = await getWarehouseList({
      or_filters: JSON.stringify([['Warehouse', 'name', '=', 'Work In Progress - V']]),
    });
    if (data.data) {
      setWarehouses(data.data);
      setIsLoading(false);
      if (
        data.data.length > 0 &&
        (selectedWarehouse === '' ||
          !data.data.find((warehouse) => warehouse.name === selectedWarehouse))
      ) {
        setSelectedWarehouse(data.data[0].name);
      }
    }
    // setSelectedWarehouse('all');
  };
  useEffect(() => {
    fetchData();
  }, []);

  const intl = useIntl();
  const extraPage = [
    <Skeleton key={'warehouseSelector'} loading={isLoading} active>
      <ProFormSelect
        formItemProps={{
          style: {
            margin: 0,
            padding: 0,
          },
        }}
        onChange={(e: any) => {
          setSelectedWarehouse(e);
        }}
        width={200}
        name={'storage_id'}
        placeholder={intl.formatMessage({
          id: 'storage-management.storage-detail.select_storage',
        })}
        fieldProps={{
          defaultValue: selectedWarehouse,
        }}
        // label={intl.formatMessage({
        //   id: 'warehouse-management.warehouse-name',
        // })}
        showSearch
        request={async (option) => {
          const res = [
            {
              label: intl.formatMessage({ id: 'common.all' }),
              value: 'all',
            },
          ];
          return res.concat(
            warehouses
              .filter((item) =>
                toLowerCaseNonAccentVietnamese(item.label || '').includes(
                  toLowerCaseNonAccentVietnamese(option.keyWords),
                ),
              )
              .map((item) => ({
                label: item.label,
                value: item.name,
              })),
          );
        }}
        key={'warehouseSelector'}
      />
    </Skeleton>,
    canCreateTransaction && (
      <Dropdown
        key={'dropdown-action'}
        menu={{
          items: [
            {
              label: <ImportVoucher onSuccess={handleRefresh} />,
              key: 'importVoucher',
            },
            {
              key: 'exportVoucher',
              label: <ExportVoucher key={'exportVoucher'} onSuccess={handleRefresh} />,
            },
            {
              key: 'reconVoucher',
              label: <StockReconciliation key={'reconVoucher'} onSuccess={handleRefresh} />,
            },
            {
              key: 'materialIssue',
              label: <MaterialIssue key={'materialIssue'} onSuccess={handleRefresh} />,
            },
            {
              key: 'materialReceipt',
              label: <MaterialReceipt onSuccess={handleRefresh} />,
            },
            {
              key: 'materialTransfer',
              label: <MaterialTransfer onSuccess={handleRefresh} />,
            },
          ],
        }}
      >
        {/* <a onClick={(e) => e.preventDefault()}>
          <Space>
            {intl.formatMessage({ id: 'warehouse-management.create-voucher' })}
            <DownOutlined />
          </Space>
        </a> */}
        <Button type="primary">
          <Space>
            {intl.formatMessage({ id: 'warehouse-management.create-voucher' })}
            <DownOutlined />
          </Space>
        </Button>
      </Dropdown>
    ),
  ].filter(Boolean); // Remove false values;
  const globalDateRangePicker = (
    <ProFormDateRangePicker
      formItemProps={{
        style: {
          margin: 0,
          padding: 0,
        },
      }}
      key="rangePicker"
      fieldProps={{
        defaultValue: [dayjs(dateRange[0]), dayjs(dateRange[1])],
        onChange: (e) => {
          const mapped: [string, string] = (e.map((day) => day!.format('YYYY-MM-DD')) || [
            dayjs('2023-01-01').format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD'),
          ]) as any;
          setDateRange(mapped);
        },
        format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
      }}
    />
  );
  return (
    <Access
      accessible={access.canAccessPageCategoryInventoryManagement()}
      fallback={<FallbackComponent />}
    >
      {' '}
      <PageContainerTabsWithPath
        tabItems={[
          // {
          //   tab: (
          //     <FormattedMessage
          //       id={'warehouse-management.dashboard'}
          //       defaultMessage="Dashboard"
          //     />
          //   ),
          //   key: 'dashboard',
          //   fallback: <DescriptionsSkeleton active />,
          //   children: <Dashboard />,
          //   extraPage: [globalDateRangePicker, ...extraPage],
          // },
          {
            tab: <FormattedMessage id={'warehouse-management.inventory-list'} />,
            key: 'inventory-list',
            fallback: <DescriptionsSkeleton active />,
            children: <InventoryListTable refreshIndicator={refreshIndicator} />,
            extraPage,
          },
          {
            tab: <FormattedMessage id={'warehouse-management.import-history'} />,
            key: 'import-history',
            fallback: <DescriptionsSkeleton active />,
            children: <ImportHistory refreshIndicator={refreshIndicator} />,
            extraPage: [globalDateRangePicker, ...extraPage],
          },
          {
            tab: <FormattedMessage id={'warehouse-management.export-history'} />,
            key: 'export-history',
            fallback: <DescriptionsSkeleton active />,
            children: <ExportHistory refreshIndicator={refreshIndicator} />,
            extraPage: [globalDateRangePicker, ...extraPage],
          },
          {
            tab: <FormattedMessage id={'warehouse-management.reconciliation-history'} />,
            key: 'reconciliation-history',
            fallback: <DescriptionsSkeleton active />,
            children: <ReconciliationHistory refreshIndicator={refreshIndicator} />,
            extraPage: [globalDateRangePicker, ...extraPage],
          },
          {
            tab: <FormattedMessage id={'common.stock-entry'} />,
            key: 'stock-entry',
            fallback: <DescriptionsSkeleton active />,
            children: <StockEntryHistory refreshIndicator={refreshIndicator} />,
            extraPage: [globalDateRangePicker, ...extraPage],
          },
          {
            tab: <FormattedMessage id={'common.report'} />,
            key: 'report',
            fallback: <DescriptionsSkeleton active />,
            children: <WarehouseReport refreshIndicator={refreshIndicator} />,
            extraPage: [globalDateRangePicker, ...extraPage],
          },
        ]}
        generalPath="/warehouse-management-v3/inventory"
      />
    </Access>
  );
};

export default Inventory;
