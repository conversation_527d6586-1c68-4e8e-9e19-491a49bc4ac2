import { ICheckTicket, ICheckTicketDetail } from '@/types/checkTicket.type';
import { dayjsUtil } from '@/utils/date';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Descriptions, Divider, Modal } from 'antd';
import numeral from 'numeral';
import { useEffect, useRef } from 'react';

interface Props {
  type: 'product' | 'category';
  generalData: ICheckTicket;
  isModalOpen: any;
  setIsModalOpen: any;
}
const CheckDetail = ({ type, generalData, isModalOpen, setIsModalOpen }: Props) => {
  const actionRef = useRef<ActionType>();
  const columns: ProColumns<ICheckTicketDetail>[] = [
    {
      title: (
        <FormattedMessage
          id="storage-management.category-management.object_code"
          defaultMessage=""
        />
      ),
      dataIndex: type === 'category' ? `iot_category_id` : 'entity_id',
    },
    {
      // title: <FormattedMessage id="category.product.product_name" defaultMessage="unknown" />
      title: <FormattedMessage id="storage-management.category-management.object_name" />,
      dataIndex: `${type}_label`,
    },
    {
      title: <FormattedMessage id="storage-management.category-management.present_quantity" />,
      dataIndex: 'current_quantity',
      search: false,
    },
    {
      title: <FormattedMessage id="storage-management.category-management.real_quantity" />,
      dataIndex: 'new_quantity',
      search: false,
    },
    {
      // title: <FormattedMessage id="storage-management.category-management.quality" />,
      title: <FormattedMessage id="storage-management.category-management.quality" />,
      dataIndex: 'quality_rating',
      search: false,
    },
  ];
  useEffect(() => {
    actionRef.current?.reload();
  }, [generalData]);

  return (
    <Modal
      open={isModalOpen}
      title={<FormattedMessage id="storage-management.category-management.detail" />}
      onCancel={() => setIsModalOpen(false)}
      footer={[]}
      width={800}
    >
      <Descriptions layout="vertical" size="small">
        <Descriptions.Item
          label={<FormattedMessage id={'storage-management.category-management.check_id'} />}
        >
          {generalData?.ticket_id}
        </Descriptions.Item>
        <Descriptions.Item
          label={<FormattedMessage id={'storage-management.category-management.check_date'} />}
        >
          {dayjsUtil(generalData?.check_date).format('YYYY/MM/DD')}
        </Descriptions.Item>
        <Descriptions.Item
          label={<FormattedMessage id={'storage-management.category-management.employee'} />}
        >
          {generalData?.first_name} {generalData?.last_name}
        </Descriptions.Item>
        <Descriptions.Item
          label={<FormattedMessage id={'storage-management.storage-detail.storage_name'} />}
        >
          {generalData?.storage_label}
        </Descriptions.Item>
        <Descriptions.Item
          label={<FormattedMessage id={'storage-management.category-management.check_type'} />}
        >
          {generalData?.check_type}
        </Descriptions.Item>
      </Descriptions>

      <Divider />
      <ProTable<ICheckTicketDetail>
        actionRef={actionRef}
        style={{ minWidth: '100%' }}
        size="small"
        columns={columns}
        cardBordered
        request={async (params = {}, sort, filter) => {
          try {
            return {
              data: generalData.detailList.map((item) => ({
                ...item,
                new_quantity: numeral(item.new_quantity).format('0,0.00'),
                current_quantity: numeral(item.current_quantity).format('0,0.00'),
              })),
              success: true,
            };
          } catch (error) {
            return {
              success: false,
            };
          }
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
        }}
        rowKey="id"
        search={false}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        pagination={{
          pageSize: 10,
        }}
        toolbar={{
          multipleLine: false,
        }}
        toolBarRender={false}
        dateFormatter="string"
      />
    </Modal>
  );
};

export default CheckDetail;
