import FallbackComponent from '@/components/FallbackContent';
import { sscriptGeneralList } from '@/services/sscript';
import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Access, useAccess, useIntl, useModel, useSearchParams } from '@umijs/max';
import { ConfigProvider, Space } from 'antd';
import { useRef } from 'react';
import CreateRoleForm from './Components/CreateNewRole';
import DeleteRole from './Components/DeleteRole';
import UpdateRoleForm from './Components/UpdateNewRole';

interface ActionType {
  reload: (resetPageIndex?: boolean) => void;
  reloadAndRest: () => void;
  reset: () => void;
  clearSelected?: () => void;
  startEditable: (rowKey: string) => boolean;
  cancelEditable: (rowKey: string) => boolean;
}

const CustomerUser: React.FC = () => {
  const tableRef = useRef<ActionType>();
  const { formatMessage } = useIntl();
  const [searchParams, setSearchParams] = useSearchParams();
  const { initialState } = useModel('@@initialState');
  const customer_name = initialState?.currentUser?.customer_id;
  if (!customer_name) return <></>;
  const access = useAccess();
  const canRead = access.canAccessPageRoleManagement();
  const canCreate = access.canCreateInRoleManagement();
  const canUpdate = access.canUpdateInRoleManagement();
  const canDelete = access.canDeleteInRoleManagement();

  const columns: ProColumns<API.User>[] = [
    {
      title: formatMessage({
        id: 'common.action',
      }),
      dataIndex: 'name',
      render: (dom: any, entity: any) => {
        return (
          <Space size="middle">
            {canUpdate && <UpdateRoleForm refreshFnc={reloadTable} user_info={entity} />}
            {canDelete && <DeleteRole refreshFnc={reloadTable} user_info={entity} />}
          </Space>
        );
      },
      fixed: 'left',
      width: 30,
    },
    {
      title: 'ID',
      dataIndex: 'name',
      width: 80,
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: formatMessage({
        id: 'common.role',
      }),
      dataIndex: 'label',
      width: 80,
    },
    {
      title: 'Sections',
      dataIndex: 'sections',
      width: 80,
      ellipsis: true,
    },
  ];

  const reloadTable = async () => {
    tableRef.current?.reload();
  };

  return (
    <Access accessible={canRead} fallback={<FallbackComponent />}>
      <PageContainer>
        <ConfigProvider>
          <ProTable<API.User, API.PageParams>
            size="small"
            actionRef={tableRef}
            rowKey="name"
            request={async (params, sort, filter) => {
              let order_by = 'modified desc';
              if (Object.keys(sort).length) {
                order_by = `${Object.keys(sort)[0]} ${
                  Object.values(sort)[0] === 'ascend' ? 'asc' : 'desc'
                }`;
              }
              const { current, pageSize } = params;
              type fieldKeyType = keyof typeof params;
              const searchFields = Object.keys(params).filter((field: string) => {
                const value = params[field as fieldKeyType];
                return field !== 'current' && field !== 'pageSize' && value !== 'all';
              });
              const filterArr = searchFields.map((field) => {
                const value = params[field as fieldKeyType];
                return ['iot_customer_user', field, 'like', `%${value}%`];
              });

              if (customer_name) {
                filterArr.push(['iot_dynamic_role', 'iot_customer', 'like', customer_name]);
              }

              try {
                const result = await sscriptGeneralList({
                  doc_name: 'iot_dynamic_role',
                  filters: filterArr,
                  page: current ? current : 0 + 1,
                  size: pageSize,
                  fields: ['*'],
                  order_by: order_by,
                });
                console.log('result', result);
                return {
                  data: result.data,
                  success: true,
                  total: result.pagination.totalElements,
                };
              } catch (error) {
                console.log(error);
              } finally {
              }
            }}
            bordered
            columns={columns}
            search={false}
            toolBarRender={() => {
              if (canCreate) {
                return [
                  <CreateRoleForm
                    refreshFnc={reloadTable}
                    customer_id={customer_name}
                    key="create"
                  />,
                ];
              } else return [];
            }}
            pagination={{
              defaultPageSize: 20,
              showSizeChanger: true,
              pageSizeOptions: ['20', '50', '100'],
            }}
          />
        </ConfigProvider>
      </PageContainer>
    </Access>
  );
};

export default CustomerUser;
