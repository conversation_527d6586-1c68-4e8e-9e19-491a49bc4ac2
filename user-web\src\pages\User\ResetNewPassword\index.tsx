import Footer from '@/components/Footer';
import { forgotPassword, login, resetPasswordByToken } from '@/services/auth';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { LoginForm, ProFormText } from '@ant-design/pro-components';
import { history, Link, useModel, useSearchParams } from '@umijs/max';
import { Col, message, Row, Alert } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { getUserInfo } from '@/services/user';

const ForgotPassword: React.FC = () => {
  const [sendSucces, setSendSucess] = useState(false);
  const [sendFail, setSendFail] = useState(false);

  const [searchParams, setSearchParams] = useSearchParams();
  const { initialState, setInitialState } = useModel('@@initialState');

  const key = searchParams.get("key");
  if (!key) {
    message.error("Invalid Reset Password Link");
    history.push("/")
    return <></>
  };

  const handleSubmit = async (values: { new_password: string, confirm_password: string }) => {
    try {
      // 登录
      const result = await resetPasswordByToken({
        key: key,
        new_password: values.new_password
      });
      const userdata = result?.result?.user;
      console.log(userdata);
      await setInitialState((s: any) => ({ ...s, currentUser: userdata }));
      message.success("Reset Password Successfully!");
      setSendSucess(true);
      window.location.href = window.location.origin;
      // setTimeout(() => {
      //   history.push("/dashboard");
      // }, 5000);

    } catch (error: any) {
      if (error.response) {
        switch (error.response.status) {
          case 410:
            message.error("The reset password link has either been used before or is invalid");
            setSendFail(true);
            break;
          default:
            message.error(`Error: ${error.response.data?.message}`);
            break;
        }
      }
    };
  };
  const validatePassword = (rule: any, value: any, callback: (arg0?: string) => void) => {
    if (value !== document.getElementById('new_password')?.value) {
      callback('Password and confirm password do not match');
    } else {
      callback();
    }
  };

  return (
    <Row className={styles.container}>
      {/* <Col md={10} xs={0} className={styles.side}>
        <img className={styles.bg} src="/images/bg.png" alt="bg" />
      </Col> */}
      <Col md={24} xs={24}>
        <div className={styles.content}>
          <LoginForm
            disabled={sendSucces}
            logo={<img style={{ width: '150px' }} alt="logo" src="/viis_logo.svg" />}
            title={<div style={{ marginTop: '20px' }}>Xác nhận mật khẩu mới</div>}
            subTitle={
              <>

              </>
            }
            initialValues={{
              autoLogin: true,
            }}
            onFinish={async (values: { new_password: string, confirm_password: string }) => {
              await handleSubmit(values);
            }}
            submitter={{
              searchConfig: {
                submitText: 'Thay đổi mật khẩu',
              },
            }}
          >
            {sendFail ?
              <Alert
                message="Error!"
                description="The reset password link has either been used before or is invalid"
                type="error"
                showIcon
                style={{ marginBottom: '20px' }}
              /> : <></>
            }
            {sendSucces ?
              <Alert
                message="Success!"
                description="Welcome back, wait a few seconds before returning to the dashboard"
                type="success"
                showIcon
                style={{ marginBottom: '20px' }}
              /> : <></>
            }
            <ProFormText.Password
              name="new_password"
              label="New Password"
              placeholder="New Password"
              rules={[
                { required: true },
                { min: 6, message: 'Password at least 6 characters' },
                { max: 40, message: 'Password up to 40 characters' },
              ]}
            />
            <ProFormText.Password
              name="confirm_password"
              label="Confirm Password"
              placeholder="Confirm Password"
              rules={[{ required: true, validator: validatePassword }]}
            />

          </LoginForm>
          <div
            style={{
              marginBottom: 24,
            }}
          >
            <div
              style={{
                textAlign: 'center',
              }}
            >
              Đã có tài khoản?
              <Link to={"/user/login"}> Đăng nhập</Link>
            </div>
          </div>
        </div>
        <Footer />
      </Col>
    </Row>
  );
};

export default ForgotPassword;
