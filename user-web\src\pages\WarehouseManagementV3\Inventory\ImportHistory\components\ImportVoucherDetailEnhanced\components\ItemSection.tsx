// components/ItemSection.tsx
import DownloadButton from '@/components/DownloadFileButton';
import FormTreeSelectSearch from '@/components/Form/FormTreeSelectSearch';
import FormUploadExcelFile from '@/components/UploadExcelFile';
import FormUploadFiles from '@/components/UploadFIles';
import { useUpdatePurchaseReceipt } from '@/pages/WarehouseManagementV3/hooks/useUpdatePurchaseReceipt';
import { openInNewTab } from '@/services/utils';
import { PlusOutlined, PrinterOutlined } from '@ant-design/icons';
import { ProForm } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Button, Col, Divider, Row, Space, Spin } from 'antd';
import { FC } from 'react';
import { useImportVoucherLogic } from '../hooks/useImportVoucherDetailLogic';
import { useImportVoucherDetailStore } from '../stores/importVoucherDetailStore';
import { IImportVoucherItem } from '../types';
import ImportVoucherTable from './ImportVoucherTable';

export const ItemSection: FC = () => {
  const { selectedWarehouse, treeData, selectedItems, setSelectedItems, savedVoucherData } =
    useImportVoucherDetailStore();
  const { run: update, loading: updating } = useUpdatePurchaseReceipt();
  const { handleAddItems, handleExcelImport } = useImportVoucherLogic();
  // Tạo wrapper function
  const handleSetData: React.Dispatch<React.SetStateAction<IImportVoucherItem[]>> = (value) => {
    if (typeof value === 'function') {
      setSelectedItems(selectedItems);
    } else {
      setSelectedItems(value);
    }
  };
  return (
    <>
      <ProForm.Group
        title={
          <div style={{ marginBottom: '-20px' }}>
            <FormattedMessage id={'warehouse-management.import-voucher.item_list'} />
          </div>
        }
      >
        <FormTreeSelectSearch
          name={'items'}
          fieldProps={{
            treeData: treeData,
          }}
          label={<FormattedMessage id={'warehouse-management.export-voucher.item_name'} />}
          colProps={{ span: 8 }}
        />
        <div style={{ marginLeft: 5 }}>
          <Row align="middle" justify="start">
            <Col>
              <FormUploadExcelFile
                formItemName="item_list"
                label={<FormattedMessage id="action.import-from-excel" />}
                onExcelDataLoaded={handleExcelImport}
              />
            </Col>
            <Col style={{ marginLeft: 5, paddingTop: 5 }}>
              <DownloadButton
                filePath="/private/files/stock_voucher.xlsx"
                buttonName="common.form.excel_template"
              />
            </Col>
          </Row>
        </div>
      </ProForm.Group>

      <ProForm.Group>
        <Row style={{ width: '100%', marginLeft: '4px' }}>
          <Col span={18}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddItems}
                disabled={!selectedWarehouse}
              >
                <FormattedMessage id={'warehouse-management.export-voucher.add_item'} />
              </Button>
            </Space>
          </Col>
        </Row>
      </ProForm.Group>
      <Row gutter={[8, 8]} style={{ marginTop: '4vh', marginLeft: '0.5vh' }}>
        <Space>
          <Spin spinning={updating}>
            <FormUploadFiles
              maxSize={10}
              isReadonly={false}
              initialImages={
                savedVoucherData && savedVoucherData.file_path ? savedVoucherData.file_path : ''
              }
              formItemName={'file_path'}
              // label={formatMessage({
              //   id: 'common.form.document',
              // })}
              fileLimit={20}
              onValueChange={async (value) => {
                await update({
                  name: savedVoucherData.name,
                  file_path: value,
                });
              }}
            />
          </Spin>
          <Button
            style={{ marginBottom: '3.5vh' }}
            disabled={false}
            key={'download'}
            icon={<PrinterOutlined />}
            onClick={() =>
              openInNewTab(
                `/warehouse-management-v3/to-pdf?type=import&id=${savedVoucherData.name}`,
              )
            }
          >
            {<FormattedMessage id={'common.print_receipt'} />}
          </Button>
        </Space>
      </Row>
      <Divider></Divider>

      <ImportVoucherTable data={selectedItems} setData={handleSetData} />
    </>
  );
};
