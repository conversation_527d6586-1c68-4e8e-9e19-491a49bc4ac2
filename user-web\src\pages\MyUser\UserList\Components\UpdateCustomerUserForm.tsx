import { Button, Col, Form, Input, message, Modal, Row, Select } from 'antd';
import { useState } from 'react';
const { Item } = Form;

import vietnam_location from '@/helpers/tree.json';
import { getDynamicRole, updateCustomerUser } from '@/services/customerUser';
import { generalCreate, generalUpdate, sscriptGeneralList } from '@/services/sscript';
import { toLowerCaseNonAccentVietnamese } from '@/services/utils';
import { IIotCustomerUser } from '@/types/auth.type';
import { ProFormSelect } from '@ant-design/pro-components';
import { useIntl, useModel } from '@umijs/max';
import TextArea from 'antd/es/input/TextArea';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

const { Option } = Select;

const UpdateCustomerUserForm = (params: { refreshFnc: any; customerUser: IIotCustomerUser }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [stateOption, setStateOption] = useState([]);
  const [wardOption, setWardOption] = useState([]);
  const [userCredential, setUserCredential] = useState({});
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  const isAdmin = currentUser?.sections.includes('SYSTEM_ADMIN');

  const showModal = () => {
    if (params.customerUser) form.setFieldsValue(params.customerUser);
    setOpen(true);
    getUserCredentitals();
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  const handleChangeCity = (value: any) => {
    if (value) {
      const new_state = Object.keys(vietnam_location[value]['quan-huyen']).map((key) => {
        return (
          <Option key={key} value={key}>
            {vietnam_location[value]['quan-huyen'][key].name_with_type}
          </Option>
        );
      });
      form.setFieldValue('district', null);
      form.setFieldValue('ward', null);
      setStateOption(new_state);
    } else {
      form.setFieldValue('district', null);
      form.setFieldValue('ward', null);
    }
  };

  const handleChangeState = (value: any) => {
    if (value) {
      const city = form.getFieldValue('province');
      if (city) {
        const new_ward: any = Object.keys(
          vietnam_location[city]['quan-huyen'][value]['xa-phuong'],
        ).map((key) => {
          return (
            <Option key={key} value={key}>
              {vietnam_location[city]['quan-huyen'][value]['xa-phuong'][key].name_with_type}
            </Option>
          );
        });
        form.setFieldValue('ward', null);
        setWardOption(new_ward);
      }
    } else {
      form.setFieldValue('ward', null);
    }
  };
  const getUserCredentitals = async () => {
    try {
      setLoading(true);
      const filterArr = [
        ['iot_customer_user_credentials', 'user_id', 'like', params.customerUser?.name],
      ];
      const result = await sscriptGeneralList({
        doc_name: 'iot_customer_user_credentials',
        filters: filterArr,
        page: 1,
        size: 1,
        fields: ['*'],
      });
      if (result.data.length > 0) {
        setUserCredential(result.data[0]);
      }
    } catch (error: any) {
      message.error(error.toString());
    } finally {
      setLoading(false);
    }
  };
  const { formatMessage } = useIntl();

  if (loading) return <>loading...</>;
  return (
    <>
      <Button type="link" onClick={showModal}>
        {formatMessage({
          id: 'common.edit',
        })}
      </Button>
      <Modal
        title={formatMessage({
          id: 'common.edit',
        })}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              let {
                email,
                phone_number,
                province,
                district,
                ward,
                address,
                description,
                first_name,
                last_name,
                iot_dynamic_role,
                is_deactivated,
              } = value;
              console.log('iot_dynamic_role', value);
              let province_str,
                district_str,
                ward_str = '';
              province_str = vietnam_location[province]?.name_with_type || null;
              if (district)
                district_str =
                  vietnam_location[province]?.['quan-huyen']?.[district]['name_with_type'];

              if (ward)
                ward_str =
                  vietnam_location[province]?.['quan-huyen']?.[district]?.['xa-phuong']?.[ward]?.[
                    'name_with_type'
                  ];

              province = province_str;
              district = district_str;
              ward = ward_str;

              // await generalUpdate('iot_customer_user', params.customerUser?.name, {
              //   data: {
              //     first_name,
              //     last_name,
              //     email,
              //     phone_number,
              //     province,
              //     district,
              //     ward,
              //     address,
              //     description,
              //     iot_dynamic_role,
              //     is_deactivated,
              //   },
              // });
              await updateCustomerUser({
                name: params.customerUser?.name,
                first_name,
                last_name,
                email,
                phone_number,
                province,
                district,
                ward,
                address,
                description,
                iot_dynamic_role,
                is_deactivated,
              });
              message.success('Success!');
              hideModal();
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={16}>
            <Col className="gutter-row" md={6}>
              <Item
                label={formatMessage({
                  id: 'common.last_name',
                })}
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="last_name"
              >
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item
                label={formatMessage({
                  id: 'common.first_name',
                })}
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="first_name"
              >
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item
                label="Email"
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                  {
                    type: 'email',
                    message: 'Vui lòng nhập đúng định dạng email',
                  },
                ]}
                name="email"
              >
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item
                label="Phone"
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="phone_number"
              >
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item label="Province" labelCol={{ span: 24 }} name="province">
                <Select
                  allowClear
                  showSearch
                  style={{
                    width: '100%',
                  }}
                  onChange={handleChangeCity}
                  filterOption={(input, option) =>
                    toLowerCaseNonAccentVietnamese(option!.children as unknown as string).includes(
                      toLowerCaseNonAccentVietnamese(input),
                    )
                  }
                >
                  {Object.keys(vietnam_location).map((key) => {
                    return (
                      <Option key={key} value={key}>
                        {vietnam_location[key].name}
                      </Option>
                    );
                  })}
                </Select>
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item label="District" labelCol={{ span: 24 }} name="district">
                <Select
                  allowClear
                  showSearch
                  style={{
                    width: '100%',
                  }}
                  onChange={handleChangeState}
                  filterOption={(input, option) =>
                    toLowerCaseNonAccentVietnamese(option!.children as unknown as string).includes(
                      toLowerCaseNonAccentVietnamese(input),
                    )
                  }
                >
                  {stateOption}
                </Select>
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item label="Ward" labelCol={{ span: 24 }} name="ward">
                <Select
                  allowClear
                  showSearch
                  style={{
                    width: '100%',
                  }}
                  filterOption={(input, option) =>
                    toLowerCaseNonAccentVietnamese(option!.children as unknown as string).includes(
                      toLowerCaseNonAccentVietnamese(input),
                    )
                  }
                >
                  {wardOption}
                </Select>
              </Item>
            </Col>
            <Col className="gutter-row" md={6}>
              <Item label="Address" labelCol={{ span: 24 }} name="address">
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item label="Description" labelCol={{ span: 24 }} name="description">
                <TextArea rows={5} placeholder="maxLength is 100" maxLength={100} />
              </Item>
            </Col>
          </Row>
          <Row>
            <Col className="gutter-row" md={24}>
              <Item
                label={formatMessage({
                  id: 'common.role',
                })}
                required
                rules={[{ required: true, message: 'Vui lòng chọn vai trò' }]}
              >
                <ProFormSelect
                  initialValue={params.customerUser.iot_dynamic_role}
                  rules={[{ required: true, message: 'Vui lòng chọn vai trò' }]}
                  name={'iot_dynamic_role'}
                  showSearch
                  request={async (option) => {
                    const roleList = await getDynamicRole();
                    return roleList.map((item: any) => ({
                      label: item.label,
                      value: item.name,
                    }));
                  }}
                />
              </Item>
            </Col>
          </Row>
          {isAdmin && (
            <></>
            // <Row>
            //   <Col className="gutter-row" md={24}>
            //     <Item
            //       label={formatMessage({
            //         id: 'common.active',
            //       })}
            //     >
            //       <ProFormSwitch
            //         initialValue={!params.customerUser.is_deactivated}
            //         name={'is_deactivated'}
            //       />
            //       {/* <Switch /> */}
            //     </Item>
            //   </Col>
            // </Row>
          )}
        </Form>

        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          onFinish={async (value: any) => {
            try {
              let { new_password } = value;

              const user_id = params.customerUser.name;
              let salt = bcrypt.genSaltSync(10);
              let hash = bcrypt.hashSync(new_password, salt);
              if (userCredential?.name) {
                await generalUpdate('iot_customer_user_credentials', userCredential?.name, {
                  data: {
                    name: userCredential?.name,
                    user_id,
                    password: hash,
                  },
                });
              } else {
                await generalCreate('iot_customer_user_credentials', {
                  data: {
                    id: uuidv4(),
                    user_id,
                    password: hash,
                  },
                });
              }

              message.success('Success!');
              hideModal();
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={5}>
            {Object.keys(userCredential).length ? (
              <>
                {/* <Col className="gutter-row" md={24}>
                                <h3>User credential ID: {userCredential?.name}</h3>
                            </Col> */}
                <Col className="gutter-row" md={12}>
                  <Item
                    label={formatMessage({ id: 'common.reset_password' })}
                    labelCol={{ span: 12 }}
                    name="new_password"
                    rules={[
                      {
                        required: true,
                        message: 'Bắt buộc điền',
                      },
                    ]}
                  >
                    <Input />
                  </Item>
                </Col>
                <Col className="gutter-row" md={12}>
                  <Button type="primary" htmlType="submit">
                    {formatMessage({ id: 'common.confirm' })}
                  </Button>
                </Col>
              </>
            ) : (
              <>
                <Col className="gutter-row" md={24}>
                  <h3>
                    {formatMessage({ id: 'common.does_not_have_an_account_create_a_new_account' })}
                  </h3>
                </Col>
                <Col className="gutter-row" md={12}>
                  <Item
                    label="Password"
                    labelCol={{ span: 12 }}
                    name="new_password"
                    rules={[
                      {
                        required: true,
                        message: 'Bắt buộc điền',
                      },
                    ]}
                  >
                    <Input />
                  </Item>
                </Col>
                <Col className="gutter-row" md={12}>
                  <Button type="primary" htmlType="submit">
                    {formatMessage({
                      id: 'common.create_an_account',
                    })}
                  </Button>
                </Col>
              </>
            )}
          </Row>
        </Form>
        {/* phân quyền */}
        {/* <Decentralization user={params.customerUser} onUpdateSuccess={params.refreshFnc} /> */}

        {/* <Row gutter={[5, 5]}>
          <Col md={24}>
            <h3>
              {formatMessage({
                id: 'common.delete_user',
              })}
            </h3>
          </Col>
          <Col md={24}>
            <RemoveCustomerUser
              credential={userCredential.name}
              name={params.customerUser?.name}
              refreshFnc={async () => {
                hideModal();
                if (params.refreshFnc) {
                  await params.refreshFnc();
                }
              }}
            />
          </Col>
        </Row> */}
      </Modal>
    </>
  );
};

export default UpdateCustomerUserForm;
