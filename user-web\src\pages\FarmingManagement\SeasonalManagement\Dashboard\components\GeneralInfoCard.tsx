import { getGeneralCropStatistics } from '@/services/cropStatistic';
import { formatMoney } from '@/utils/format';
import { history, useIntl } from '@umijs/max';
import { Card, Col, List, Row, Skeleton, Typography } from 'antd';
import { useEffect, useState } from 'react';

const { Text, Title } = Typography;

const bodyStyle: React.CSSProperties = {
  fontSize: 20,
  color: '#44C4A1',
  fontWeight: 'bold',
};

const GeneralInfoCard = () => {
  const [generalInfos, setGeneralInfos] =
    useState<
      { title: string; value: string; imageUrl: string; color: string; onClick?: () => void }[]
    >();
  const [loading, setLoading] = useState(true);
  const intl = useIntl();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const res = await getGeneralCropStatistics();
      const contentList = [
        {
          imageUrl: 'url(/images/crop-dashboard-card/working.jpg)',
          title: intl.formatMessage({ id: 'homeTab.ongoing' }),
          value: res?.inProgressCrop || '0',
          color: '#2770BA',
          onClick: () => {
            history.push('/farming-management/seasonal-management?tab=ongoing');
          },
        },
        {
          imageUrl: 'url(/images/crop-dashboard-card/finished.jpg)',
          title: intl.formatMessage({ id: 'homeTab.completed' }),
          value: res?.finishedCrop || '0',
          color: '#44C4A1',
          onClick: () => {
            history.push('/farming-management/seasonal-management?tab=completed');
          },
        },
        {
          imageUrl: 'url(/images/crop-dashboard-card/calendar2.png)',
          title: intl.formatMessage({ id: 'homeTab.cropsIsOverdue' }),
          value: res?.getOutdatedCrop || '0',
          color: '#ec6e5f',
        },
        {
          imageUrl: 'url(/images/crop-dashboard-card/farmer.jpg)',
          title: intl.formatMessage({ id: 'homeTab.staff' }),
          value: res?.totalEmployee || '0',
          color: '#A46ECF',
          onClick: () => {
            history.push('/employee-management/employee-list');
          },
        },
        {
          imageUrl: 'url(/images/crop-dashboard-card/vat-tu-su-dung.jpg)',
          title: intl.formatMessage({ id: 'homeTab.total_value_of_materials_used' }),
          value: formatMoney(res?.totalMaterialUsedPrice),
          color: '#FF659C',
        },
        {
          imageUrl: 'url(/images/crop-dashboard-card/san-luong-hien-tai.jpg)',
          title: intl.formatMessage({ id: 'homeTab.total_current_output_value' }),
          value: formatMoney(res?.totalHarvestedProductPrice),
          color: '#22b3cd',
        },
        {
          imageUrl: 'url(/images/crop-dashboard-card/thu-hoach.jpg)',
          title: intl.formatMessage({ id: 'homeTab.total_value_harvested' }),
          value: formatMoney(res?.totalHarvestedProductPrice),
          color: '#94C137',
        },
        {
          imageUrl: 'url(/images/crop-dashboard-card/so-loai-cay-trong.jpg)',
          title: intl.formatMessage({ id: 'homeTab.number_of_crop_types' }),
          value: formatMoney(res?.plantCount),
          color: '#3694b8',
          onClick: () => {
            history.push('/farming-management/seasonal-management?tab=plant');
          },
        },
        {
          imageUrl: 'url(/images/crop-dashboard-card/khu-vuc.jpg)',
          title: intl.formatMessage({ id: 'homeTab.number_of_zone' }),
          value: res?.zoneCount,
          color: '#2e90d8',
          onClick: () => {
            history.push('/farming-management/seasonal-management?tab=zone');
          },
        },
        {
          imageUrl: 'url(/images/crop-dashboard-card/task.jpg)',
          title: intl.formatMessage({ id: 'homeTab.today_task' }),
          value: res?.todayTaskCount,
          color: '#4aca8a',
          onClick: () => {
            history.push('/farming-management/workflow-management');
          },
        },
      ];
      setGeneralInfos(contentList);
      setLoading(false);
    };
    fetchData();
  }, [intl]);

  return (
    <>
      <List
        grid={{
          lg: 3,
          xl: 4,
          xxl: 5,
          gutter: [
            { xxl: 10, xs: 10, xl: 10 },
            { xxl: 10, xs: 10, xl: 10 },
          ],
          md: 2,
          sm: 2,
          xs: 1,
        }}
        dataSource={generalInfos || new Array(10).fill({})}
        renderItem={(item, index) => (
          <List.Item key={index}>
            <a onClick={item.onClick}>
              <Card
                bodyStyle={{
                  backgroundColor: item.color || '#f0f0f0',
                  color: 'white',
                }}
                title={<div />}
                headStyle={{
                  height: 100,
                  backgroundImage: item.imageUrl,
                  backgroundRepeat: 'no-repeat',
                  backgroundSize: 'cover',
                  backgroundColor: '#E4FFF4',
                }}
              >
                <Skeleton loading={loading} active>
                  <Row style={{ height: 54 }} align="middle">
                    <Col span={24} style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text
                        style={{
                          color: 'white',
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        {item.title}
                      </Text>
                      <Title
                        level={5}
                        style={{
                          color: 'white',
                          display: 'flex',
                          alignItems: 'center',
                          margin: 0,
                        }}
                      >
                        {item.value}
                      </Title>
                    </Col>
                  </Row>
                </Skeleton>
              </Card>
            </a>
          </List.Item>
        )}
      />
    </>
  );
};

export default GeneralInfoCard;
