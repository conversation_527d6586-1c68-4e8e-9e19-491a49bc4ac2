/* eslint-disable no-useless-escape */
import { ProForm } from '@ant-design/pro-components';
import { App, Tabs } from 'antd';
import { FC, useEffect } from 'react';

import withTriggerFormModal, { TriggerFormModalProps } from '@/HOC/withTriggerFormModal';
import { TableInventoryDetail } from '@/pages/WarehouseManagementV3/Inventory/InventoryListTable/components/InventoryDetail';
import { ModalForm } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { useGetDetailsProductItemV3 } from '../hooks/useGetDetailsProductItemV3';
import { useUpdateProductItemV3 } from '../hooks/useUpdateProductItemV3';
import { TableCustomerData } from './components/CustomerData';
import InfoTab from './components/InforTab';
import { TableSupplierData } from './components/SupplierData';

type AddNewZoneInProjectProps = TriggerFormModalProps<{
  defaultValue?: any;
  id?: string;
}>;
const ContentForm: FC<AddNewZoneInProjectProps> = ({
  open,
  onOpenChange,
  onSuccess,
  modalProps,
  trigger,
}) => {
  const { formatMessage } = useIntl();
  const [form] = ProForm.useForm();
  const { message } = App.useApp();
  const {
    loading,
    run: getDetail,
    data,
  } = useGetDetailsProductItemV3({
    onSuccess(data) {
      form.setFieldsValue({
        ...data,
      });
    },
  });
  useEffect(() => {
    if (modalProps?.id) {
      getDetail({
        name: modalProps.id,
      });
    }
  }, [modalProps?.id]);

  const { run: updateProductItemV3 } = useUpdateProductItemV3({
    onSuccess,
  });
  const intl = useIntl();
  return (
    <ModalForm
      loading={loading || undefined}
      form={form}
      title={<FormattedMessage id={'category.material-management.add_category'} />}
      name="'category.material-management.update_category'"
      width={1000}
      onFinish={async (values: any) => {
        if (!data) {
          formatMessage({
            id: 'common.error',
          });
          return false;
        }
        //in values.uoms array, if object has isAddNew = true, delete field name from object
        values.uoms = values.uoms.map((item: any) => {
          if (item.isAddNew) {
            delete item.name;
            delete item.isAddNew;
          }
          return item;
        });
        const dataReq: Parameters<typeof updateProductItemV3>[0] = {
          name: data.name,
          image: values.image,
          stock_uom: values.stock_uom,
          standard_rate: values.standard_rate, //gia ban mac dinh
          label: values.label,
          item_name: values.item_name,
          // "iot_customer": "09cb98f0-e0f5-11ec-b13b-4376e531a14a", //optional
          item_group: values.item_group,
          valuation_rate: values.valuation_rate, //gia mua mac dinh
          uoms: values.uoms,
          description: values.description,
          max_stock_level: values.max_stock_level,
          min_stock_level: values.min_stock_level,
        };
        await updateProductItemV3(dataReq);
        onSuccess?.();
        return true;
      }}
      open={open}
      onOpenChange={onOpenChange}
      trigger={trigger}
    >
      <Tabs defaultActiveKey="1">
        <Tabs.TabPane tab={intl.formatMessage({ id: 'common.info' })} key="1">
          <InfoTab form={form} data={data} />
        </Tabs.TabPane>
        <Tabs.TabPane tab={intl.formatMessage({ id: 'common.attribute' })} key="2">
          {/* Content for Thuộc tính tab */}
        </Tabs.TabPane>
        <Tabs.TabPane tab={intl.formatMessage({ id: 'common.supplier' })} key="3">
          <TableSupplierData entity={data} key={data?.item_code || data?.name} />
          {/* Content for Nhà cung cấp tab */}
        </Tabs.TabPane>
        <Tabs.TabPane tab={intl.formatMessage({ id: 'common.customer' })} key="4">
          {/* Content for Khách hàng tab */}
          <TableCustomerData entity={data} key={data?.item_code || data?.name} />
        </Tabs.TabPane>
        <Tabs.TabPane tab={intl.formatMessage({ id: 'common.inventory-voucher' })} key="5">
          {/* Content for Thẻ kho tab */}
          <TableInventoryDetail key={data?.item_code || data?.name} entity={data} />
        </Tabs.TabPane>
      </Tabs>
      {/* <ProFormTextArea
        label={<FormattedMessage id={'common.form.description'} />}
        labelCol={{ span: 24 }}
        name="description"
      /> */}
    </ModalForm>
  );
};

const UpdateProduct = withTriggerFormModal({
  defaultTrigger: ({ changeOpen, disabled }) => (
    <a
      href="#"
      onClick={(e) => {
        e.preventDefault();
        if (!disabled) {
          changeOpen(true);
        }
      }}
    ></a>
  ),
  contentRender: ContentForm,
});
export default UpdateProduct;
