import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getCropList, updateCrop } from '@/services/cropManager';
import { uploadFile } from '@/services/fileUpload';
import { getListFileUrlFromString } from '@/services/utils';
import { FooterToolbar, ProForm } from '@ant-design/pro-components';
import { history, useAccess, useIntl, useRequest } from '@umijs/max';
import { App, Button, Card, Divider, Space, Spin, UploadFile } from 'antd';
import { nanoid } from 'nanoid';
import { FC, ReactNode, useEffect, useState } from 'react';
import CropItemTransactionTable from './components/CropTransactionTables';
import ItemsStatistic from './components/ItemsStatistic';
import ProductionQuantityStatistic from './components/ProductionQuantityStatistic';
import DetailedInfo from './DetailedInfo';

interface GeneralInfoProps {
  children?: ReactNode;
  onSuccess?: () => void;
  cropId: string;
}
type IFormData = {
  label: string;
  zone_id: string;
  date_range: [string, string];
  square: number;
  plant_id: string;
  img?: UploadFile[];
  avatar?: any;
  description?: string;
  quantity_estimate?: number;
  status?: string;
};

const GeneralInfo: FC<GeneralInfoProps> = ({ onSuccess, cropId }) => {
  const { message } = App.useApp();
  const [submitting, setSubmitting] = useState(false);
  const intl = useIntl();
  const onFinish = async (values: IFormData) => {
    setSubmitting(true);
    try {
      // create
      const dataUpdated = await updateCrop({
        name: cropId,
        label: values.label,
        zone_id: values.zone_id,
        square: values.square,
        plant_id: values.plant_id,
        start_date: values.date_range[0],
        end_date: values.date_range[1],
        description: values.description,
        image: null,
        avatar: null,
        status: values.status,
        quantity_estimate: values.quantity_estimate,
      });

      // upload file
      if (values.img && (values.img || []).length > 0) {
        // kiểm tra các file đã upload

        const filesUploaded = values.img.filter((item) => !item.originFileObj);
        const filesNotUpload = values.img.filter((item) => item.originFileObj);
        // upload bất kể thành công hay ko
        const uploadListRes = await Promise.allSettled(
          filesNotUpload.map(async (item) => {
            return await uploadFile({
              docType: DOCTYPE_ERP.iotCrop,
              docName: dataUpdated.data.name,
              file: item.originFileObj as any,
            });
          }),
        );
        // check if() 1 vài upload failed
        const checkUploadFailed = uploadListRes.find((item) => item.status === 'rejected');
        if (checkUploadFailed) {
          message.error({
            content: 'Some file upload failed',
          });
        }

        // update img path
        const arrFileUrl = uploadListRes
          .reduce<string[]>(
            (prev, item) =>
              item.status === 'fulfilled' ? [...prev, item?.value?.data?.message?.file_url] : prev,
            [],
          )
          .filter((item) => typeof item === 'string')
          // thêm file đã upload
          .concat(filesUploaded.map((item) => item.url as string));

        if (arrFileUrl.length > 0) {
          await updateCrop({
            name: dataUpdated.data.name,
            image: arrFileUrl.join(','),
            zone_id: dataUpdated.data.zone_id,
          });
        }
      }

      //upload avatar
      if (values.avatar && (values.avatar || []).length > 0) {
        // kiểm tra các file đã upload

        const filesUploaded = values.avatar.filter((item: any) => !item.originFileObj);
        const filesNotUpload = values.avatar.filter((item: any) => item.originFileObj);
        // upload bất kể thành công hay ko
        const uploadListRes = await Promise.allSettled(
          filesNotUpload.map(async (item: any) => {
            return await uploadFile({
              docType: DOCTYPE_ERP.iotCrop,
              docName: dataUpdated.data.name,
              file: item.originFileObj as any,
            });
          }),
        );
        // check if() 1 vài upload failed
        const checkUploadFailed = uploadListRes.find((item) => item.status === 'rejected');
        if (checkUploadFailed) {
          message.error({
            content: 'Some file upload failed',
          });
        }

        // update avatar path
        const arrFileUrl = uploadListRes
          .reduce<string[]>(
            (prev, item) =>
              item.status === 'fulfilled' ? [...prev, item?.value?.data?.message?.file_url] : prev,
            [],
          )
          .filter((item) => typeof item === 'string')
          // thêm file đã upload
          .concat(filesUploaded.map((item: any) => item.url as string));

        if (arrFileUrl.length > 0) {
          await updateCrop({
            name: dataUpdated.data.name,
            avatar: arrFileUrl[0],
            zone_id: dataUpdated.data.zone_id,
          });
        }
      }

      message.success({
        content: 'Updated successfully',
      });
      onSuccess?.();
      // history.push('/farming-management/seasonal-management');
      // window.location.reload();
      return true;
    } catch (error) {
      message.error({
        content: 'Error, please try again',
      });
      return false;
    } finally {
      setSubmitting(false);
    }
  };
  const [form] = ProForm.useForm();
  const { loading, run: getDetail } = useRequest(
    () =>
      getCropList({
        size: 1,
        page: 1,
        filters: [[DOCTYPE_ERP.iotCrop, 'name', '=', cropId]],
      }),
    {
      manual: true,
      onError() {
        message.error({
          content: 'Can not get crop information, please try again',
        });
      },
      onSuccess(res) {
        const dataFound = res[0];
        if (!dataFound) return;

        form.setFieldsValue({
          label: dataFound.label,
          zone_id: dataFound.zone_id,
          date_range: [dataFound.start_date, dataFound.end_date],
          square: dataFound.square,
          plant_id: dataFound.plant_id,
          status: dataFound.status,
          description: dataFound.description,
          quantity_estimate: dataFound.quantity_estimate,
          img: getListFileUrlFromString({
            arrUrlString: dataFound.image,
          }).map((item) => ({
            uid: nanoid(),
            status: 'done',
            url: item,
            type: 'image/*',
          })),
          avatar: getListFileUrlFromString({
            arrUrlString: dataFound.avatar,
          }).map((item) => ({
            uid: nanoid(),
            status: 'done',
            url: item,
            type: 'image/*',
          })),
        });
      },
    },
  );
  useEffect(() => {
    if (cropId) {
      getDetail();
    }
  }, [cropId]);

  const access = useAccess();
  const canUpdate = access.canUpdateInSeasonalManagement();
  return (
    <>
      <Spin spinning={loading}>
        <ProForm<IFormData> onFinish={onFinish} form={form} submitter={false} grid={true}>
          <Space
            size={'large'}
            direction="vertical"
            style={{
              width: '100%',
            }}
          >
            <DetailedInfo cropId={cropId} form={form} />
            {/* <Card title="Hình ảnh / Video mô tả ">
              <ProFormUploadButton
                accept="image/*"
                listType="picture-card"
                icon={<CameraFilled />}
                title=""
                name="img"
              />
            </Card> */}
            <Card title={intl.formatMessage({ id: 'seasonalTab.basicStatistics' })}>
              <ItemsStatistic cropId={cropId} />
              <Divider />
              <ProductionQuantityStatistic cropId={cropId} />
              {/* <Divider />
              <WorksheetStatistic cropId={cropId} /> */}
            </Card>
            <Card title={intl.formatMessage({ id: 'common.crop_item_transactions_table' })}>
              {' '}
              <CropItemTransactionTable cropId={cropId} />
            </Card>
          </Space>
        </ProForm>
      </Spin>
      <FooterToolbar>
        <Button
          onClick={() => {
            history.back();
          }}
        >
          {intl.formatMessage({ id: 'common.cancel' })}
        </Button>
        {canUpdate && (
          <Button
            loading={loading || submitting}
            type="primary"
            onClick={() => {
              form.submit();
            }}
          >
            {intl.formatMessage({ id: 'common.save' })}
          </Button>
        )}
      </FooterToolbar>
    </>
  );
};

export default GeneralInfo;
