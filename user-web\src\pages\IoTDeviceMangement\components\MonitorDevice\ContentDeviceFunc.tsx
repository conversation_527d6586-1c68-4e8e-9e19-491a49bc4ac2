import { IIotProductionFunction } from '@/types/IIotProductionFunction';
import { Col, Row } from 'antd';
import FuncRenderBool from './FuncRender/FuncRenderBool';
import FuncRenderCheckboxBit from './FuncRender/FuncRenderCheckboxBit';
import FuncRenderEnum from './FuncRender/FuncRenderEnum';
import FuncRenderValue from './FuncRender/FuncRenderValue/FuncRenderValue';

const CheckTypeAndRender = ({ fnc, deviceId }: { fnc: any; deviceId: string }) => {
  console.log('debug fnc', fnc);
  switch (fnc.data_type) {
    case 'Enum':
      return <FuncRenderEnum fnc={fnc} deviceId={deviceId} />;
    case 'Bool':
      return <FuncRenderBool fnc={fnc} />;
    case 'Value':
      return <FuncRenderValue fnc={fnc} deviceId={deviceId} />;
    case 'Raw':
      return <FuncRenderValue fnc={fnc} deviceId={deviceId} />;
    case 'Checkbox-bit':
      return <FuncRenderCheckboxBit fnc={fnc} deviceId={deviceId} />;
    default:
      return null;
  }
};

const ContentDeviceFunc = ({
  deviceFunctions,
  deviceId,
}: {
  deviceId: string;
  deviceFunctions: any[];
}) => {
  if (!deviceFunctions.length) return <></>;
  return (
    <Row gutter={[16, 16]}>
      {deviceFunctions.map((d: IIotProductionFunction) => {
        return (
          <Col md={d.md_size ? d.md_size : 12} key={'func-' + d.name}>
            <CheckTypeAndRender deviceId={deviceId} fnc={d} />
          </Col>
        );
      })}
    </Row>
  );
};

export default ContentDeviceFunc;
