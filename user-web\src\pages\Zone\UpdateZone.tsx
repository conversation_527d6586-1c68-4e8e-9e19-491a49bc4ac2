import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import useFormAddress from '@/components/Form/FormAddress';
import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import withTriggerFormModal, { TriggerFormModalProps } from '@/HOC/withTriggerFormModal';
import { getProjectList } from '@/services/project';
import { updateZone } from '@/services/zoneManager';
import { EditOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { App, Button, Col, InputNumber, Row } from 'antd';
import { FC, useEffect } from 'react';
import useGetDetailZone from './hook/useGetDetailZone';

type AddNewZoneInProjectProps = TriggerFormModalProps<{
  defaultValue?: any;
  zoneId: string;
}>;

const ContentForm: FC<AddNewZoneInProjectProps> = ({
  open,
  onOpenChange,
  onSuccess,
  modalProps,
  trigger,
}) => {
  const { formatMessage } = useIntl();
  const [form] = ProForm.useForm();
  const { data, loading } = useGetDetailZone({
    zoneId: modalProps?.zoneId,
    onSuccess(res) {
      form.setFieldsValue({
        label: res.label,
        image: res.image,
        city: res.city,
        district: res.district,
        ward: res.ward,
        address: res.address,
        project_id: res.project_id,
        // map: {
        //   lng: res.lot,
        //   lat: res.lat,
        // },
        lot: res.lot,
        lat: res.lat,
      });
    },
  });

  const { message } = App.useApp();
  useEffect(() => {
    try {
      if (modalProps?.defaultValue) {
        form.setFieldsValue(modalProps.defaultValue);
      }
    } catch (error) {}
  }, [modalProps?.defaultValue]);
  const { districtElement, cityElement, wardElement, detailsElement } = useFormAddress({
    form: form,
    initialValue: {
      city: data?.city,
      district: data?.district,
      ward: data?.ward,
      address: data?.address,
    },
    formProps: {
      city: {
        // width: 'md',
        name: 'city',
      },
      // district: {
      //   width: 'md',
      // },
      // ward: {
      //   width: 'md',
      // },
      // address: {
      //   width: 'md',
      // },
    },
  });
  return (
    <ModalForm
      name="update:zone"
      form={form}
      loading={loading}
      width={500}
      onFinish={async (values) => {
        try {
          if (!data?.name) {
            message.error(
              formatMessage({
                id: '',
              }),
            );
            return false;
          }
          await updateZone({
            name: data?.name,
            label: values.label,
            image: values.image,
            project_id: values.project_id,
            city: values.city,
            district: values.district,
            ward: values.ward,
            address: values.address,
            lot: values.lot,
            lat: values.lat,
          });
          message.success(
            formatMessage({
              id: 'common.success',
            }),
          );
          onSuccess?.();
          return true;
        } catch (error: any) {
          message.error(error?.message?.toString?.());
          return false;
        }
      }}
      open={open}
      onOpenChange={onOpenChange}
      trigger={trigger}
    >
      <Row>
        <Col span={24}>
          <FormUploadsPreviewable
            formItemName={'image'}
            label={formatMessage({ id: 'common.image_preview' })}
            fileLimit={1}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <ProFormText
            label={formatMessage({
              id: 'common.zone_name',
            })}
            name="label"
            rules={[{ required: true }]}
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            name="project_id"
            label={formatMessage({ id: 'common.project' })}
            showSearch
            request={async (params) => {
              let filters = [];
              if (params.keyWords) {
                filters.push([DOCTYPE_ERP.iotProject, 'label', 'like', `%${params.keyWords}%`]);
              }
              const res = await getProjectList({
                page: params.page,
                size: params.current,
                filters: filters,
              });
              return res.data.map((item) => ({
                label: item.label,
                value: item.name,
              }));
            }}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <ProFormTextArea label={formatMessage({ id: 'common.description' })} name="description" />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>{cityElement}</Col>
        <Col span={12}>{districtElement}</Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>{wardElement}</Col>
        <Col span={12}>{detailsElement}</Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <ProForm.Item
            label={formatMessage({ id: 'diary.longitude' })}
            name="lot"
            rules={[{ required: true, message: 'Please enter longitude' }]}
          >
            <InputNumber style={{ width: '100%' }} />
          </ProForm.Item>
        </Col>
        <Col span={12}>
          <ProForm.Item
            label={formatMessage({ id: 'diary.latitude' })}
            name="lat"
            rules={[{ required: true, message: 'Please enter latitude' }]}
          >
            <InputNumber style={{ width: '100%' }} />
          </ProForm.Item>
        </Col>
      </Row>
      {/* <ProFormItem label={'Select Longitude, Latitude'} name="map">
        <ProFormMap />
        </ProFormItem> */}
    </ModalForm>
  );
};

const UpdateZone = withTriggerFormModal({
  defaultTrigger: ({ changeOpen }) => (
    <Button icon={<EditOutlined />} onClick={() => changeOpen(true)} />
  ),
  contentRender: ContentForm,
});
export default UpdateZone;
