import { DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { getItemGroupV3 } from '@/services/InventoryManagementV3/Item-group';
import { getUOM_v3 } from '@/services/InventoryManagementV3/uom';
import { ProFormTextArea } from '@ant-design/pro-components';
import { ProFormDigit, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Col, Divider, Row } from 'antd';
import { FC, useState } from 'react';
import CreateCategory from '../../../Category/components/CreateCategory';
import CreateUOMs from '../../../UOM/UOMList/components/Create';
import BARModal from '../../BARModal';
import QRModal from '../../QRModal';
import UnitConversion from '../UnitConversion';

const InfoTab: FC<{ form: any; data: any }> = ({ form, data }) => {
  const [refreshKey, setRefreshKey] = useState(0);
  const refreshOptions = () => {
    console.log('refreshOptions called');
    setRefreshKey((prevKey) => prevKey + 1);
  };
  const intl = useIntl();
  return (
    <Row gutter={16} justify="space-between" align="bottom">
      <Col span={8}>
        <FormUploadsPreviewable
          label={<FormattedMessage id={'common.form.image'} />}
          fileLimit={1}
          formItemName={'image'}
          initialImages={form.getFieldValue('image')}
        />
      </Col>
      <Col span={8} style={{ padding: '10px' }}>
        <div style={{ paddingBottom: '10px' }}>Bar code</div>
        <BARModal data={data} />
      </Col>
      <Col span={8} style={{ padding: '10px' }}>
        <div style={{ paddingBottom: '10px' }}>QR code</div>
        <QRModal data={data} />
      </Col>
      <Col span={24}>
        <Row gutter={16} justify="space-between" align="bottom">
          <Col span={8}>
            <ProFormText
              width={'sm'}
              name="item_name"
              label={<FormattedMessage id="common.item_name" />}
              rules={[
                {
                  required: true,
                  message: 'Bắt buộc điền',
                },
              ]}
            />
            <ProFormSelect
              key={refreshKey + 'uom'}
              label={<FormattedMessage id="category.material-management.unit" />}
              name="stock_uom"
              width={'sm'}
              rules={[
                {
                  required: true,
                },
              ]}
              showSearch
              fieldProps={{
                dropdownRender: (menu) => (
                  <div>
                    {menu}
                    <Divider style={{ margin: '4px 0' }} />
                    <div style={{ display: 'flex', flexWrap: 'nowrap', padding: 8 }}>
                      <CreateUOMs buttonType="link" refreshFnc={refreshOptions} />
                    </div>
                  </div>
                ),
              }}
              request={async (params) => {
                const filters: any[] = [];
                const res = await getUOM_v3({
                  page: 1,
                  size: DEFAULT_PAGE_SIZE_ALL,
                  filters: filters,
                  order_by: 'uom_name ASC',
                });
                return res.data.map((item) => ({
                  label: `${item.uom_name}`,
                  value: item.name,
                }));
              }}
            />
          </Col>
          <Col span={8}>
            <ProFormText
              width={'sm'}
              name="label"
              label={<FormattedMessage id="category.material-management.category_name" />}
              rules={[
                {
                  required: true,
                  message: 'Bắt buộc điền',
                },
              ]}
            />

            <ProFormDigit
              width={'sm'}
              name="standard_rate"
              min={1}
              // rules={[
              //   {
              //     required: true,
              //   },
              // ]}
              label={intl.formatMessage({
                id: 'common.default-price',
              })}
              fieldProps={{
                formatter: (value: any) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
                parser: (value: any) => value.replace(/\$\s?|(,*)/g, ''),
              }}
            />
          </Col>
          <Col span={8}>
            <ProFormSelect
              key={refreshKey + 'item_group'}
              width={'sm'}
              rules={[
                {
                  required: true,
                  message: 'Bắt buộc điền',
                },
              ]}
              allowClear
              label={<FormattedMessage id="category.material-management.category_type" />}
              name="item_group"
              showSearch
              fieldProps={{
                dropdownRender: (menu) => (
                  <div>
                    {menu}
                    <Divider style={{ margin: '4px 0' }} />
                    <div style={{ display: 'flex', flexWrap: 'nowrap', padding: 8 }}>
                      <CreateCategory refreshFnc={refreshOptions} inLineButton={true} />
                    </div>
                  </div>
                ),
              }}
              request={async (params) => {
                const filters: any[] = [];
                // if (params.keyWords) {
                //   filters.push(['','label', 'like', `%${params.keyWords}%`]);
                // }
                const res = await getItemGroupV3({
                  page: 1,
                  size: DEFAULT_PAGE_SIZE_ALL,
                  filters: filters,
                  order_by: 'label ASC',
                });
                return res.data.map((item: any) => ({
                  label: item.label,
                  value: item.name,
                }));
              }}
            />
            <ProFormDigit
              width={'sm'}
              name="valuation_rate"
              min={1}
              // rules={[
              //   {
              //     required: true,
              //   },
              // ]}
              label={intl.formatMessage({
                id: 'common.default-purchase-price',
              })}
              fieldProps={{
                formatter: (value: any) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
                parser: (value: any) => value.replace(/\$\s?|(,*)/g, ''),
              }}
            />
          </Col>
        </Row>
      </Col>
      <Col span={8}>
        <ProFormDigit
          width={'sm'}
          label={intl.formatMessage({ id: 'common.max_stock_level' })}
          name="max_stock_level"
          fieldProps={{
            formatter: (value: any) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
            parser: (value: any) => value.replace(/\$\s?|(,*)/g, ''),
          }}
        ></ProFormDigit>
      </Col>
      <Col span={8}>
        <ProFormDigit
          width={'sm'}
          label={intl.formatMessage({ id: 'common.min_stock_level' })}
          name="min_stock_level"
          fieldProps={{
            formatter: (value: any) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
            parser: (value: any) => value.replace(/\$\s?|(,*)/g, ''),
          }}
        ></ProFormDigit>
      </Col>
      <Col span={8}>
        <ProFormTextArea name="description" label={<FormattedMessage id="common.description" />} />
      </Col>
      <UnitConversion key={refreshKey} />
    </Row>
  );
};

export default InfoTab;
