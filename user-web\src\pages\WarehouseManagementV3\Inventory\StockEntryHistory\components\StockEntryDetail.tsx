import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import { IStockEntryDetailItem } from '@/components/StockEntryButtons/Interfaces/StockEntryInterfaces';
import FormUploadFiles from '@/components/UploadFIles';
import { getStockEntryNoteDetail } from '@/services/stock/stockEntry';
import { formatMoneyNumeral, formatNumeral, openInNewTab } from '@/services/utils';
import { PrinterOutlined } from '@ant-design/icons';
import {
  ActionType,
  ProColumns,
  ProForm,
  ProFormDatePicker,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { FormattedMessage, useAccess, useIntl, useRequest } from '@umijs/max';
import { Button, Col, Modal, Row } from 'antd';
import { useEffect, useRef, useState } from 'react';

interface Props {
  name: string;
  isModalOpen: boolean;
  setIsModalOpen: any;
  onSuccess?: () => void;
}
const StockEntryDetail = ({ name, isModalOpen, setIsModalOpen }: Props) => {
  const actionRef = useRef<ActionType>();
  const [form] = ProForm.useForm();
  const [items, setItems] = useState<IStockEntryDetailItem[]>([]);
  const [fileList, setFileList] = useState<string | undefined>(undefined);
  const [type, setType] = useState<string>();

  const { data, loading, refresh, run } = useRequest(getStockEntryNoteDetail, {
    manual: true,
    onError(error) {
      console.log('error', error.message);
    },
  });
  const handleReload = () => {
    actionRef.current?.reload();
    refresh();
  };
  const access = useAccess();
  const canReadInventoryValue = access.canReadCategoryInventoryFieldLevelManagement();

  const columns: ProColumns<IStockEntryDetailItem>[] = [
    {
      title: <FormattedMessage id="common.index" />,
      dataIndex: 'index',
      render(dom, entity, index, action, schema) {
        return <div>{index + 1}</div>;
      },
      width: 15,
    },
    {
      title: <FormattedMessage id="warehouse-management.stock-entry-history.item_id" />,
      dataIndex: 'item_name',
      width: 20,
    },
    {
      title: <FormattedMessage id="warehouse-management.stock-entry-history.item_label" />,
      dataIndex: 'item_label',
      width: 20,
    },
    {
      title: <FormattedMessage id="common.unit" />,
      dataIndex: 'uom_name',
      width: 20,
    },
    {
      title: <FormattedMessage id="common.quantity" />,
      dataIndex: 'qty',
      width: 20,
      render(dom, entity, index, action, schema) {
        return <div>{formatNumeral(entity.qty)}</div>;
      },
    },
    {
      title: <FormattedMessage id="warehouse-management.stock-entry-history.rate" />,
      dataIndex: 'rate',
      width: 20,
      render(dom, entity, index, action, schema) {
        return <div>{formatMoneyNumeral(entity.valuation_rate)}</div>;
      },
      hideInTable: !canReadInventoryValue,
    },
    {
      title: <FormattedMessage id="warehouse-management.stock-entry-history.amount" />,
      dataIndex: 'amount',
      width: 20,
      render(dom, entity, index, action, schema) {
        return <div>{formatMoneyNumeral(entity.amount)}</div>;
      },
      hideInTable: !canReadInventoryValue,
    },
  ];

  useEffect(() => {
    run({ name });
  }, [name]);
  useEffect(() => {
    form.resetFields();
    form.setFieldsValue(data);
    // console.log('Data is ', data);
    // form.setFieldValue('warehouse_label', data?.items?.at(0)?.warehouse_label);
    form.setFieldValue('user', `${data?.user_first_name} ${data?.user_last_name}`);
    form.setFieldsValue({
      s_warehouse_label: data?.items[0].s_warehouse_label,
      t_warehouse_label: data?.items[0].t_warehouse_label,
    });
    setType(data?.purpose);
    setItems(data?.items || []);
  }, [data]);
  const { formatMessage } = useIntl();
  return (
    <Modal
      open={isModalOpen}
      title={<FormattedMessage id={'warehouse-management.stock-entry-history.detail'} />}
      onCancel={() => {
        setIsModalOpen(false);
        handleReload();
      }}
      footer={[]}
      width={1000}
    >
      <ProForm submitter={false} disabled form={form} layout="vertical" grid={true}>
        <ProFormText
          label={<FormattedMessage id={'warehouse-management.stock-entry-history.id'} />}
          colProps={{
            sm: 24,
            md: 6,
          }}
          name={'name'}
          width="md"
        />
        <ProFormDatePicker
          label={<FormattedMessage id={'warehouse-management.stock-entry-history.date'} />}
          colProps={{
            sm: 24,
            md: 6,
          }}
          name={'posting_date'}
          width="md"
          fieldProps={{
            format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
          }}
        />
        <ProFormText
          label={<FormattedMessage id={'warehouse-management.stock-entry-history.purpose'} />}
          colProps={{
            sm: 24,
            md: 6,
          }}
          name={'purpose'}
          width={'md'}
        />
        <ProFormText
          label={<FormattedMessage id={'common.assigned_to'} />}
          colProps={{
            sm: 24,
            md: 6,
          }}
          name={'user'}
          width="md"
        />

        {type === 'Material Receipt' ? (
          ''
        ) : (
          <ProFormText
            label={<FormattedMessage id={'warehouse-management.from-warehouse-name'} />}
            colProps={{
              sm: 24,
              md: 6,
            }}
            name={'s_warehouse_label'}
            width="md"
          />
        )}
        {type === 'Material Issue' ? (
          ''
        ) : (
          <ProFormText
            label={<FormattedMessage id={'warehouse-management.to-warehouse-name'} />}
            colProps={{
              sm: 24,
              md: 6,
            }}
            name={'t_warehouse_label'}
            width="md"
          />
        )}
        <ProFormTextArea
          label={<FormattedMessage id={'common.description'} />}
          colProps={{
            sm: 24,
            md: 12,
          }}
          name={'description'}
          width="xl"
        />
      </ProForm>
      <div
        style={{
          marginLeft: 5,
          maxWidth: 500,
        }}
      >
        {' '}
        <Row gutter={16}>
          <Col span={10}>
            <FormUploadFiles
              maxSize={10}
              isReadonly={true}
              initialImages={data?.file_path}
              formItemName={'file_path'}
              label={formatMessage({
                id: 'common.form.document',
              })}
              fileLimit={20}
            />
          </Col>
          <Col span={12}>
            <Button
              key={'download'}
              icon={<PrinterOutlined />}
              onClick={() => {
                let receiptType = '';
                switch (type) {
                  case 'Material Issue':
                    receiptType = 'materialIssue';
                    break;
                  case 'Material Receipt':
                    receiptType = 'materialReceipt';
                    break;
                  case 'Material Transfer':
                    receiptType = 'materialTransfer';
                    break;
                  default:
                    receiptType = 'notHandledType';
                    break;
                }
                openInNewTab(
                  `/warehouse-management-v3/to-pdf?type=${receiptType}&id=${data?.name}`,
                );
              }}
            >
              {<FormattedMessage id={'common.print_receipt'} />}
            </Button>
          </Col>
        </Row>
      </div>

      {/* <Row>
        <Col span={4} offset={16}>
          <FormattedMessage id="warehouse-management.stock-entry-history.total_price" />
        </Col>
        <Col span={4}>{formatNumeral(data?.value_difference)}</Col>
      </Row> */}
      <br />
      <ProTable<IStockEntryDetailItem>
        columns={columns}
        cardBordered
        size="small"
        dataSource={items}
        rowKey={'name'}
        search={false}
      />
    </Modal>
  );
};

export default StockEntryDetail;
