import {
  DEFAULT_DATE_FORMAT_WITHOUT_TIME,
  DEFAULT_PAGE_SIZE_ALL,
} from '@/common/contanst/constanst';
import { getPlantList } from '@/services/plants';
import { getZoneList } from '@/services/zoneManager';
import { PlusOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { Link, useIntl } from '@umijs/max';
import { Button, Card, Col, DatePicker, Input, Row } from 'antd';
import { FC } from 'react';
const { RangePicker } = DatePicker;

interface FilterProps {
  canCreateCrop?: boolean;
  handleSearch?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSearchDate?: (val: any) => void;
  onPlantChange?: (val: any) => void;
  onZoneChange?: (val: any) => void;
}

const Filter: FC<FilterProps> = ({
  handleSearchDate,
  handleSearch,
  canCreateCrop,
  onPlantChange,
  onZoneChange,
}) => {
  const intl = useIntl();
  return (
    <Card bordered>
      <Row gutter={16} align="middle">
        <Col span={8}>
          <Input
            addonBefore={intl.formatMessage({ id: 'seasonalTab.season' })}
            onChange={handleSearch}
          />
        </Col>
        <Col span={4}>
          <RangePicker
            format={DEFAULT_DATE_FORMAT_WITHOUT_TIME}
            onCalendarChange={handleSearchDate}
          />
        </Col>
        <Col span={4}>
          <ProFormSelect
            noStyle
            width={'100%'}
            placeholder={intl.formatMessage({ id: 'common.select_plant' })}
            onChange={(v: any) => {
              onPlantChange?.(v);
            }}
            showSearch
            request={async () => {
              const res: any = await getPlantList({
                page: 1,
                size: DEFAULT_PAGE_SIZE_ALL,
              });
              return res.data.map((item: any) => ({
                label: item.label,
                value: item.name,
              }));
            }}
          />
        </Col>
        <Col span={4}>
          <ProFormSelect
            noStyle
            width={'100%'}
            placeholder={intl.formatMessage({ id: 'seasonalTab.selectArea' })}
            onChange={(v: any) => {
              onZoneChange?.(v);
            }}
            showSearch
            request={async () => {
              const res: any = await getZoneList({
                page: 1,
                size: DEFAULT_PAGE_SIZE_ALL,
              });
              return res.data.map((item: any) => ({
                label: item.label,
                value: item.name,
              }));
            }}
          />
        </Col>
        <Col span={4} style={{ textAlign: 'right' }}>
          {canCreateCrop && (
            <Link to="/farming-management/seasonal-management/create">
              <Button icon={<PlusOutlined />} type="primary">
                {intl.formatMessage({ id: 'seasonalTab.createSeason' })}
              </Button>
            </Link>
          )}
        </Col>
      </Row>
    </Card>
  );
};

export default Filter;
