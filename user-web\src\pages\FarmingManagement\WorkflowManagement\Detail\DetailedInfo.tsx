import { DEFAULT_DATE_AND_HH_MM_FORMAT, DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { getCustomerUserList } from '@/services/customerUser';
import { getFarmingPlanList, getFarmingPlanState } from '@/services/farming-plan';
import { removeFile, uploadFile } from '@/services/uploadFile';
import { generateAPIPath } from '@/services/utils';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ProFormDateTimePicker,
  ProFormGroup,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { FormattedMessage, useAccess, useIntl } from '@umijs/max';
import { Button, Card, Col, message, Row, Space, UploadProps } from 'antd';
import { useEffect, useState } from 'react';
import ProFormTagSelect from '../TagManager/ProFormTagSelect';
import QRCodeModal from './components/QRCodeModal';

const DetailedInfo = ({
  task_id,
  loading,
  farming_plan,
  onEditTagSuccess,
  imageUrl,
  setImageUrl,
}: {
  task_id?: string;
  imageUrl: string;
  loading: boolean;
  farming_plan: string;
  onEditTagSuccess?: () => void;
  setImageUrl: (imageUrl: string) => void;
}) => {
  const [selectedPlan, setSelectedPlan] = useState('');
  const [planStateOptions, setPlanStateOptions] = useState<any>([]);
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [isOpen, setOpen] = useState(false);

  const [localImageUrl, setLocalImageUrl] = useState<string>();
  useEffect(() => {
    const fetchData = async () => {
      try {
        let filters = [];
        if (farming_plan || selectedPlan) {
          if (selectedPlan) {
            console.log('new plan selected');
            filters.push(['iot_farming_plan_state', 'farming_plan', 'like', selectedPlan]);
          } else if (farming_plan) {
            console.log('old plan selected');
            filters.push(['iot_farming_plan_state', 'farming_plan', 'like', farming_plan]);
          }
          const res = await getFarmingPlanState({
            page: 1,
            size: DEFAULT_PAGE_SIZE_ALL,
            filters: filters,
          });
          console.log('state in plan', res);
          setPlanStateOptions(
            res.data.map((item) => {
              return {
                label: item.label,
                value: item.name,
              };
            }),
          );
        }
      } catch (error: any) {
        message.error(error.toString());
      }
    };
    fetchData();
  }, [selectedPlan, farming_plan]);

  const uploadButton = (
    <div>
      {uploading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  const handleRemove = async (file: any) => {
    console.log('file to remove', file);
    if (file.status === 'done') {
      try {
        await removeFile({
          fid: file.uid,
          dt: '',
          dn: '',
        });
      } catch (error) {
        message.error('Delete Error,try again!');
      } finally {
      }
    }
  };

  const handleChangeUpload: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];
    // 1. Limit the number of uploaded files
    // Only to show two recent uploaded files, and old ones will be replaced by the new
    // 2. Read from response and show file link
    newFileList = newFileList.map((file: any) => {
      if (file.response) {
        // Component will show file.url as link
        file.uid = file.response.name;
        const userdata = JSON.parse(localStorage.getItem('token') || '{}');
        file.url = generateAPIPath(
          'api/v2/file/download?file_url=' + file.response.file_url + '&token=' + userdata?.token,
        );
        file.raw_url = file.response.file_url;
      }
      return file;
    });
    setFileList(newFileList);
    if (newFileList.length) {
      setLocalImageUrl(newFileList[0]?.url || '');
      setImageUrl(newFileList[0]?.url || '');
    }
  };

  const handleUpload = async (options: any) => {
    const { onSuccess, onError, file } = options;
    try {
      setUploading(true);
      const res = await uploadFile({
        file,
        doctype: '',
        docname: '',
        is_private: 1,
        folder: 'Home/Attachments',
        optimize: false,
      });
      console.log('file_url', res);
      onSuccess(res.message);
    } catch (err) {
      console.log('Error: ', err);
      onError({ err });
    } finally {
      setUploading(false);
    }
  };

  useEffect(() => {
    setLocalImageUrl(imageUrl);
    return () => {
      // Cleanup code (if needed)
    };
  }, [imageUrl]); // Empty dependency array means this effect runs once after the initial render

  const access = useAccess();
  const canUpdateTask = access.canUpdateInWorkFlowManagement();
  const intl = useIntl();
  return (
    <Row gutter={[5, 5]}>
      <Col md={24}>
        <Card title={intl.formatMessage({ id: 'task.detailed_info' })}>
          <Row>
            <Space>
              {canUpdateTask && (
                <FormUploadsPreviewable
                  initialImages={imageUrl}
                  fileLimit={20}
                  label={<FormattedMessage id="task.description_images" />}
                  formItemName={'image'}
                />
              )}
              <QRCodeModal taskId={task_id} />
            </Space>
          </Row>
          {/* <Row>
            <Space>
              {localImageUrl ? (
                <>
                  <Button
                    onClick={() => {
                      console.log('delete bro');
                      setFileList([]);
                      setLocalImageUrl('');
                      setImageUrl('');
                    }}
                  >
                    <DeleteFilled />
                  </Button>
                </>
              ) : (
                <></>
              )}
            </Space>
          </Row> */}
          <Row gutter={[5, 5]}>
            <Col md={24}>
              <ProFormGroup>
                <ProFormText
                  label={<FormattedMessage id="task.task_name" />}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  name="label"
                  colProps={{
                    md: 8,
                    sm: 24,
                  }}
                />
                <ProFormText
                  hidden
                  name="image"
                  colProps={{
                    md: 8,
                    sm: 24,
                  }}
                />
                <ProFormSelect
                  label={<FormattedMessage id="task.select_crop" />}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  showSearch
                  onChange={(v: any) => {
                    setSelectedPlan(v);
                  }}
                  request={async () => {
                    const res: any = await getFarmingPlanList({
                      page: 1,
                      size: DEFAULT_PAGE_SIZE_ALL,
                    });
                    return res.data.map((item: any) => ({
                      label: item.label,
                      value: item.name,
                    }));
                  }}
                  name="farming_plan"
                  colProps={{
                    md: 8,
                    sm: 24,
                  }}
                />
                <ProFormSelect
                  label={<FormattedMessage id="task.select_stage" />}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  showSearch
                  name="farming_plan_state"
                  options={planStateOptions}
                  disabled={planStateOptions.length === 0}
                  colProps={{ md: 8, sm: 24 }}
                />

                <ProFormDateTimePicker
                  label={<FormattedMessage id="task.start_time" />}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  fieldProps={{
                    format: DEFAULT_DATE_AND_HH_MM_FORMAT,
                  }}
                  name="start_date"
                  colProps={{
                    md: 8,
                    sm: 24,
                  }}
                  width={'xl'}
                />

                <ProFormDateTimePicker
                  label={<FormattedMessage id="task.end_time" />}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  fieldProps={{
                    format: DEFAULT_DATE_AND_HH_MM_FORMAT,
                  }}
                  name="end_date"
                  colProps={{
                    md: 8,
                    sm: 24,
                  }}
                  width={'xl'}
                />
                <ProFormSelect
                  label={<FormattedMessage id="task.assigned_to" />}
                  name="assigned_to"
                  showSearch
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  request={async () => {
                    const res = await getCustomerUserList({
                      page: 1,
                      size: DEFAULT_PAGE_SIZE_ALL,
                      fields: ['name', 'first_name', 'last_name', 'email'],
                    });
                    return res.data.map((item) => ({
                      label:
                        item.first_name || item.last_name
                          ? `${item.first_name || ''} ${item.last_name || ''}`
                          : `${item.email}`,
                      value: item.name,
                    }));
                  }}
                  colProps={{ md: 8, sm: 24 }}
                />
                <ProFormSelect
                  label={<FormattedMessage id="task.related_members" />}
                  name="involve_in_users"
                  request={async () => {
                    const res = await getCustomerUserList({
                      page: 1,
                      size: DEFAULT_PAGE_SIZE_ALL,
                      fields: ['name', 'first_name', 'last_name', 'email'],
                    });
                    return res.data.map((item) => ({
                      label:
                        item.first_name || item.last_name
                          ? `${item.first_name || ''} ${item.last_name || ''}`
                          : `${item.email}`,
                      value: item.name,
                    }));
                  }}
                  mode="multiple"
                  colProps={{ md: 8, sm: 24 }}
                />
                <ProFormSelect
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  label={<FormattedMessage id="task.status" />}
                  name="status"
                  options={[
                    {
                      label: intl.formatMessage({ id: 'task.status.plan' }),
                      value: 'Plan',
                    },
                    {
                      label: intl.formatMessage({ id: 'task.status.in_progress' }),
                      value: 'In progress',
                    },
                    {
                      label: intl.formatMessage({ id: 'task.status.done' }),
                      value: 'Done',
                    },
                    {
                      label: intl.formatMessage({ id: 'task.status.pending' }),
                      value: 'Pending',
                    },
                  ]}
                  initialValue={'Plan'}
                  colProps={{ md: 8, sm: 24 }}
                />
                <ProFormTextArea label={<FormattedMessage id="task.notes" />} name="description" />

                <ProFormTagSelect onEditTagSuccess={onEditTagSuccess} />
                {/* <ProFormCheckbox
                  label={<FormattedMessage id="task.enable_tracing" />}
                  name="enable_origin_tracing"
                /> */}

                {canUpdateTask && (
                  <Button loading={loading} type="primary" htmlType="submit">
                    <FormattedMessage id="task.save_info" />
                  </Button>
                )}
              </ProFormGroup>
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>
  );
};

export default DetailedInfo;
