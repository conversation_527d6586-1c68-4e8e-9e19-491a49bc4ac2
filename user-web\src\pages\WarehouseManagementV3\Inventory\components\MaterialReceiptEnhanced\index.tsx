import { PlusSquareOutlined } from '@ant-design/icons';
import { ModalForm } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Button, Form, Popconfirm } from 'antd';
import moment from 'moment';
import { FC, useEffect, useState } from 'react';
import { MaterialReceiptForm } from './components/MaterialReceiptForm';
import { useMaterialReceiptLogic } from './hooks/useMaterialReceiptLogic';
import { useMaterialReceiptStore } from './stores/materialReceiptStore';

interface Props {
  onSuccess?: () => void;
  initialData?: any;
  autoOpen?: boolean;
  onClose?: () => void; // New callback prop to notify parent when the modal closes
}

const MaterialReceipt: FC<Props> = ({ onSuccess, initialData, autoOpen = false, onClose }) => {
  const { handleSave, handleSubmit, handleAddItems } = useMaterialReceiptLogic(onSuccess);
  const store = useMaterialReceiptStore();
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(autoOpen);

  // Open modal on mount if autoOpen is true
  useEffect(() => {
    if (autoOpen && !isModalOpen) {
      console.log('Opening modal due to autoOpen:', autoOpen);
      setIsModalOpen(true);
    }
  }, [autoOpen]);

  useEffect(() => {
    if (initialData) {
      console.log('initialData', initialData);
      form.setFieldsValue({
        name: initialData.name,
        posting_date: moment(),
        purpose: initialData.purpose,
        warehouse: initialData.warehouse,
        description: initialData.description,
      });
      store.setSavedVoucherData(initialData);
      store.setSelectedWarehouse(initialData.warehouse);
    }
  }, [initialData]);

  useEffect(() => {
    if (initialData && store.isItemTreeDataLoaded && store.form) {
      console.log('Data loaded, now setting fields and adding items');

      if (initialData.items && Array.isArray(initialData.items) && initialData.items.length > 0) {
        // Truyền nguyên mảng object vào field `items`
        store.form.setFieldsValue({ items: initialData.items });

        // Gọi handleAddItems sau một chút delay để đảm bảo form đã được cập nhật
        setTimeout(() => {
          handleAddItems();
        }, 100);
      }
    }
  }, [store.isItemTreeDataLoaded, initialData, store.form, handleAddItems]);

  const handleSaveClick = async () => {
    const values = await form.validateFields();
    await handleSave(values);
  };

  const handleSubmitClick = async () => {
    try {
      await handleSubmit();
      onSuccess?.();
      setIsModalOpen(false);
      if (onClose) onClose();
      return true;
    } catch (error) {
      console.error('Submit failed:', error);
      return false;
    }
  };

  useEffect(() => {
    store.setForm(form);
  }, [form]);

  console.log('MaterialReceipt render - isModalOpen:', isModalOpen, 'autoOpen:', autoOpen);

  return (
    <ModalForm
      title={<FormattedMessage id="warehouse-management.material-receipt" />}
      modalProps={{
        onCancel: () => {
          store.reset();
          setIsModalOpen(false);
          if (onClose) onClose();
          console.log('Modal cancelled');
        },
        destroyOnClose: true,
      }}
      trigger={
        !autoOpen ? (
          <Button icon={<PlusSquareOutlined />} type="link" style={{ color: '#629584' }}>
            <FormattedMessage id="warehouse-management.material-receipt" />
          </Button>
        ) : undefined
      }
      open={isModalOpen}
      onOpenChange={(visible) => {
        console.log('onOpenChange:', visible);
        setIsModalOpen(visible);
      }}
      width={1600}
      form={form}
      layout="vertical"
      rowProps={{ gutter: [16, 0] }}
      onFinish={handleSubmitClick}
      submitter={{
        render: (props, defaultDoms) => (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '8px',
              width: '100%',
            }}
          >
            <Button
              key="save"
              type="primary"
              onClick={handleSaveClick}
              loading={store.submitting}
              style={{ width: '200px' }}
              disabled={store.isSaved}
            >
              <FormattedMessage id="common.save" />
            </Button>
            {store.isSaved && (
              <Popconfirm
                key="submit"
                title="Bạn có chắc chắn muốn hoàn thành phiếu? Hành động này không thể hoàn tác."
                onConfirm={() => props.submit()}
              >
                <Button type="primary" style={{ width: '200px' }} loading={store.submitting}>
                  <FormattedMessage id="common.confirm" />
                </Button>
              </Popconfirm>
            )}
          </div>
        ),
      }}
      autoFocusFirstInput
      grid
    >
      <MaterialReceiptForm />
    </ModalForm>
  );
};

export default MaterialReceipt;
