import { getCropParticipantsTask<PERSON>ist, ICropItemInTask } from '@/services/crop';
import { formatDateDefault } from '@/utils/date';
import { EyeOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { App, Button, Modal, Space } from 'antd';
import { useState } from 'react';
export interface Props {
  full_name: string;
  user_id: string;
  crop_id: string;
}
const TaskParticipantsModal = ({ full_name, user_id, crop_id }: Props) => {
  const [isOpen, setOpen] = useState(false);
  const showModal = () => {
    setOpen(true);
  };

  const { message } = App.useApp();
  const hideModal = () => {
    setOpen(false);
  };

  const handleCancel = () => {
    hideModal();
  };
  const intl = useIntl();
  const columns: ProColumns<ICropItemInTask>[] = [
    {
      title: intl.formatMessage({ id: 'common.task' }),
      dataIndex: 'task_label',
      render(dom, entity, index, action, schema) {
        return (
          <a
            href={`/farming-management/workflow-management/detail/${entity.task_id}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            {entity.task_label}
          </a>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'common.start_date' }),
      dataIndex: 'start_date',
      render(dom, entity, index, action, schema) {
        return <span>{formatDateDefault(entity.start_date)}</span>;
      },
    },
    {
      title: intl.formatMessage({ id: 'common.end_date' }),
      dataIndex: 'end_date',
      render(dom, entity, index, action, schema) {
        return <span>{formatDateDefault(entity.end_date)}</span>;
      },
    },
    {
      title: intl.formatMessage({ id: 'common.form.description' }),
      dataIndex: 'description',
    },
    // {
    //   title: intl.formatMessage({ id: 'common.task_child' }),
    //   dataIndex: 'todos',
    //   // render(dom, entity, index, action, schema) {
    //   //   return (

    //   //   );
    //   // },
    // },
  ];
  return (
    <>
      <Space size={'small'}>
        <Button icon={<EyeOutlined />} onClick={showModal}></Button>
        <span>{full_name}</span>
      </Space>
      <Modal
        title={`Chi tiết sử dụng vật tư ${full_name} trong vụ mùa`}
        open={isOpen}
        onCancel={handleCancel}
        footer={null}
        width={1200}
      >
        <ProTable<ICropItemInTask>
          columns={columns}
          search={false}
          pagination={{
            pageSizeOptions: [10, 20, 50, 100],
            showSizeChanger: true,
          }}
          request={async (params, sorter, filter) => {
            try {
              const res = await getCropParticipantsTaskList({
                page: params.current,
                size: params.pageSize,
                crop_id,
                customer_user_id: user_id,
              });
              return {
                data: res.data,
                success: true,
                total: res.pagination.totalElements,
              };
            } catch (error: any) {
              message.error(`Lỗi khi kéo dữ liệu: ${error.message}`);
              return {
                success: false,
              };
            }
          }}
          rowKey={'task_id'}
        />
      </Modal>
    </>
  );
};

export default TaskParticipantsModal;
