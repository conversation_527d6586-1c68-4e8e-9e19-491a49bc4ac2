import { FunctionList } from '@/services/devices';
import { Segmented } from 'antd';
import { FC, useEffect, useMemo, useState } from 'react';
import GaugeChart from './GaugeChart';
import LineChart2 from './LineChart2';

// Đ<PERSON>nh nghĩa các loại chart hợp lệ
type ChartType = 'Gauge' | 'Line';

// Chỉ định các component chart với type cụ thể
const ChartOptions: Record<ChartType, FC<ChartProps>> = {
  Gauge: (props: ChartProps) => <GaugeChart {...props} />,
  Line: (props: ChartProps) => <LineChart2 {...props} />,
};

type ChartProps = {
  dataFunction?: FunctionList;
  deviceId: string;
  deviceKey: string;
};

const ChartRender: FC<ChartProps> = (props) => {
  // Khởi tạo localChartActive dựa trên chart_type, mặc định là 'Line' nếu không hợp lệ
  const initialChartType = props.dataFunction?.chart_type;
  const [localChartActive, setLocalChartActive] = useState<ChartType>(
    initialChartType === 'Gauge' || initialChartType === 'Line' ? initialChartType : 'Line',
  );

  // Đồng bộ localChartActive khi chart_type thay đổi
  useEffect(() => {
    const chartType = props.dataFunction?.chart_type;
    if (chartType === 'Gauge' || chartType === 'Line') {
      setLocalChartActive(chartType);
    } else {
      setLocalChartActive('Line'); // Mặc định về Line nếu chart_type không hợp lệ
    }
  }, [props.dataFunction?.chart_type]);

  // Lấy component chart dựa trên localChartActive
  const ChartActive = useMemo(() => ChartOptions[localChartActive], [localChartActive]);

  return (
    <>
      <Segmented
        options={['Line', 'Gauge']}
        value={localChartActive}
        onChange={(value) => {
          setLocalChartActive(value as ChartType); // Ép kiểu vì options chỉ có 'Line' và 'Gauge'
        }}
        style={{
          marginBottom: 16,
        }}
      />
      <ChartActive {...props} />
    </>
  );
};

export default ChartRender;
