import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import ActionHover from '@/components/ActionHover';
import { getWorkShiftList } from '@/services/attendance-v2/work-shift';
import { getParamsReqTable } from '@/services/utils';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Space } from 'antd';
import { FC, ReactNode, useEffect, useRef, useState } from 'react';
import DeleteWorkShift from './DeleteWorkShift';

interface WorkShiftListProps {
  children?: ReactNode;
  onSelect?: (workshiftId: string) => void;
  reloadKey?: string | null;
}

const WorkShiftList: FC<WorkShiftListProps> = ({ children, onSelect, reloadKey }) => {
  const { formatMessage } = useIntl();
  const actionRef = useRef<ActionType>();
  const [selectedRowKey, setSelectedRowKey] = useState<string | number>('');
  const [data, setData] = useState<any[]>([]);

  const handleReload = () => {
    setSelectedRowKey('');
    actionRef.current?.reload?.();
  };
  useEffect(() => {
    if (reloadKey) {
      handleReload();
    }
  }, [reloadKey]);
  useEffect(() => {
    if (data.length > 0 && !selectedRowKey) {
      const firstRowKey = data[0].name;
      setSelectedRowKey(firstRowKey);
      onSelect?.(firstRowKey);
    }
  }, [data]);
  return (
    <ProTable
      actionRef={actionRef}
      search={false}
      toolBarRender={() => []}
      rowKey={'name'}
      options={false} // Hide the entire toolbar
      form={{
        labelWidth: 'auto',
      }}
      // scroll={{
      //   x: 'max-content',
      // }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        defaultPageSize: 10,
      }}
      request={async (params, sort, filter) => {
        const paramsReq = getParamsReqTable({
          doc_name: DOCTYPE_ERP.iot_workshift,
          tableReqParams: {
            params,
            sort,
            filter,
          },
          defaultSort: 'name asc',
        });
        const res = await getWorkShiftList(paramsReq);
        setData(res.data);

        return {
          data: res.data,
          total: res.pagination.totalElements,
        };
      }}
      rowClassName={(record) =>
        record.name === selectedRowKey ? 'bg-emerald-100 group/action' : 'group/action'
      }
      columns={[
        {
          title: formatMessage({
            id: 'common.work_shift_code',
          }),
          dataIndex: 'workshift_code',
          render(dom, entity, index, action, schema) {
            return (
              <Button
                type="link"
                onClick={() => {
                  setSelectedRowKey(entity.name);
                  onSelect?.(entity.name);
                }}
              >
                {dom}
              </Button>
            );
          },
          width: '50%',
        },
        {
          title: formatMessage({
            id: 'common.work_shift_label',
          }),
          dataIndex: 'label',
          render: (dom, entity) => {
            return (
              <ActionHover
                actions={() => (
                  <Space>
                    <DeleteWorkShift id={entity.name} onSuccess={handleReload} />{' '}
                  </Space>
                )}
              >
                {dom}
              </ActionHover>
            );
          },
          width: '50%',
        },
      ]}
    />
  );
};

export default WorkShiftList;
