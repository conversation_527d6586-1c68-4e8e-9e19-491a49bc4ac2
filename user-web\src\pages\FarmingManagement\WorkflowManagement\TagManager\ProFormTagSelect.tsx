import { getTags } from '@/services/tag';
import { ProFormSelect } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { But<PERSON>, Divider, Space } from 'antd';
import { nanoid } from 'nanoid';
import { FC, ReactNode, useState } from 'react';
import TagManager from '.';

interface ProFormTagSelectProps {
  children?: ReactNode;
  // nếu có đổi color thì những task đã lây cũng câp nhật theo
  onEditTagSuccess?: () => void;
  initTag?: string;
}

const ProFormTagSelect: FC<ProFormTagSelectProps> = ({ children, onEditTagSuccess, initTag }) => {
  const [openTagManager, setOpenTagManager] = useState(false);
  // reload select khi sửa đổi Tag
  const [uuid, setUuid] = useState(nanoid());
  const reloadOptions = () => {
    onEditTagSuccess?.();

    setUuid(nanoid());
  };
  const intl = useIntl();
  return (
    <>
      {openTagManager && (
        <TagManager
          open={openTagManager}
          onOpenChange={setOpenTagManager}
          onSuccess={reloadOptions}
        />
      )}
      <ProFormSelect
        key={uuid}
        label={intl.formatMessage({ id: 'common.tag' })}
        name={'tag'}
        initialValue={initTag}
        fieldProps={{
          // onSra
          open: openTagManager ? false : undefined,
          optionItemRender(item) {
            return (
              <Space>
                <div
                  style={{
                    width: 18,
                    height: 18,
                    borderRadius: 100,
                    backgroundColor: item.color,
                  }}
                />
                <span>{item.label}</span>
              </Space>
            );
          },
          dropdownRender(menu) {
            return (
              <>
                {menu}
                <Divider style={{ margin: '8px 0' }} />

                <Button
                  type="link"
                  onClick={() => {
                    setOpenTagManager(true);
                  }}
                >
                  Quản lí nhãn
                </Button>
              </>
            );
          },
        }}
        showSearch
        request={async (params) => {
          const res = await getTags({
            page: 1,
            size: 50,
            filters: [
              ...(params.keyword ? [['iot_tag', 'label', 'like', `%${params.keyword}%`]] : []),
            ],
            fields: ['*'],
          });
          return res.data.map((item) => ({
            label: item.label,
            value: item.name,
            color: item.color,
          }));
        }}
      />
    </>
  );
};

export default ProFormTagSelect;
