import { useDateRangeStore } from '@/pages/WarehouseManagementV3/hooks/useDateRangeStore';
import { useSelectedWarehousedStore } from '@/pages/WarehouseManagementV3/hooks/useWarehouseStore';
import { getTotalWarehouseQty, getTotalWarerhouseItemQty } from '@/services/stock/dashboard';
import { formatNumeral } from '@/services/utils';
import { FormattedMessage, useAccess, useIntl } from '@umijs/max';
import { Space, Tag } from 'antd';
import { useEffect, useState } from 'react';

interface BarGraphData {
  label: string;
  totalAmount: string;
  backgroundColor: string;
  link: string;
}
const colors = ['#e0f7fa', '#fce4ec', '#fff3e0', '#e8f5e9', '#ede7f6', '#ffebee'];
const InventoryStatistic = () => {
  const { selectedWarehouse } = useSelectedWarehousedStore();
  const { dateRange } = useDateRangeStore();
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<BarGraphData[]>([]);
  const [stockStatus, setStockStatus] = useState();
  const intl = useIntl();
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      const fetchedData: BarGraphData[] = [];
      const filter = {
        start_date: '1900-01-01',
        end_date: '2300-01-01',
        warehouse: selectedWarehouse === 'all' ? undefined : selectedWarehouse,
      };
      const totalWarehouseQty = await getTotalWarehouseQty(filter);
      const stockStatus = await getTotalWarerhouseItemQty(filter);
      setStockStatus(stockStatus.stockStatusCounts);
      fetchedData.push({
        label: intl.formatMessage({ id: 'common.number_of_items' }),
        totalAmount: formatNumeral(totalWarehouseQty.result.unique_item_count),
        backgroundColor: colors[0],
        link: '/warehouse-management-v3/inventory/inventory-list',
      });
      fetchedData.push({
        label: intl.formatMessage({ id: 'common.stock_value' }),
        totalAmount: `${formatNumeral(totalWarehouseQty.result.total_price)} VNĐ`,
        backgroundColor: colors[1],
        link: '/warehouse-management-v3/inventory/inventory-list',
      });

      setData(fetchedData);
      setIsLoading(false);
    };

    fetchData();
  }, [selectedWarehouse, dateRange]);

  const access = useAccess();
  const readInventoryValue = access.canReadCategoryInventoryFieldLevelManagement();
  return (
    <Space>
      {readInventoryValue && (
        <Tag color={'green'}>
          {data.at(1)?.label}: {data.at(1)?.totalAmount}
        </Tag>
      )}

      <Tag color={'blue'}>
        {data.at(0)?.label}: {data.at(0)?.totalAmount}
      </Tag>
      <Tag color="gold">
        <FormattedMessage id={'common.in_stock'} />: {stockStatus?.['common.in_stock']}
      </Tag>
      <Tag color="gold">
        <FormattedMessage id={'common.out_stock'} />: {stockStatus?.['common.out_stock']}
      </Tag>
      <Tag color="gold">
        <FormattedMessage id={'common.nearly_out_of_stock'} />:{' '}
        {stockStatus?.['common.nearly_out_of_stock']}
      </Tag>
      <Tag color="gold">
        <FormattedMessage id={'common.overstock'} />: {stockStatus?.['common.overload']}
      </Tag>
    </Space>
    // <Card
    //   style={{
    //     width: '100%',
    //   }}
    // >
    //   <Skeleton loading={isLoading} />
    //   {data.length !== 0 && (
    //     <>
    //       <Row gutter={16}>
    //         <Col lg={8} sm={24}>
    //           <DetailBarGraphCard
    //             label={data.at(0)!.label}
    //             backgroundColor={data.at(0)!.backgroundColor}
    //             link={data.at(0)!.link}
    //             customInfo={data.at(0)!.totalAmount}
    //           />
    //         </Col>
    //         <Col lg={8} sm={24}>
    //           <DetailBarGraphCard
    //             label={data.at(1)!.label}
    //             backgroundColor={data.at(1)!.backgroundColor}
    //             link={data.at(1)!.link}
    //             customInfo={data.at(1)!.totalAmount}
    //           />
    //         </Col>
    //         <Col lg={8} sm={24}>
    //           <DetailBarGraphCard
    //             label={intl.formatMessage({ id: 'common.product_status' })}
    //             backgroundColor={'#EEF7FF'}
    //             link={'#'}
    //             customInfo={
    //               <Space direction="vertical" size={3}>
    //                 <p>
    //                   - <FormattedMessage id={'common.in_stock'} />:{' '}
    //                   {stockStatus?.['common.in_stock']}
    //                 </p>
    //                 <p>
    //                   - <FormattedMessage id={'common.out_stock'} />:{' '}
    //                   {stockStatus?.['common.out_stock']}
    //                 </p>
    //                 <p>
    //                   - <FormattedMessage id={'common.nearly_out_of_stock'} />:{' '}
    //                   {stockStatus?.['common.nearly_out_of_stock']}
    //                 </p>
    //                 <p>
    //                   - <FormattedMessage id={'common.overstock'} />:{' '}
    //                   {stockStatus?.['common.overload']}
    //                 </p>
    //               </Space>
    //             }
    //           />
    //         </Col>
    //       </Row>
    //     </>
    //   )}
    // </Card>
  );
};

export default InventoryStatistic;
