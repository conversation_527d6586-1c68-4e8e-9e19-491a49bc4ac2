import { COLOR_HEX, DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import { getUOM_v3 } from '@/services/InventoryManagementV3/uom';
import { DeleteOutlined, EditOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import {
  EditableFormInstance,
  EditableProTable,
  nanoid,
  ProColumns,
  ProForm,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Tooltip } from 'antd';
import { FC, ReactNode, useMemo, useRef } from 'react';

interface UnitConversionProps {
  children?: ReactNode;
}
type DataSourceType = {
  name: React.Key;
  doctype: string;
  uom?: string;
  conversion_factor: number;
};
const UnitConversion: FC<UnitConversionProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  const editorFormRef = useRef<EditableFormInstance<DataSourceType>>();
  const nameKey = 'uoms';
  const form = ProForm.useFormInstance();
  const intl = useIntl();
  const columns = useMemo<ProColumns<DataSourceType>[]>(
    () => [
      {
        title: formatMessage({
          id: 'common.unit',
        }),
        dataIndex: 'uom',
        valueType: 'select',
        request: async (params) => {
          const filters: any[] = [];
          const res = await getUOM_v3({
            page: 1,
            size: DEFAULT_PAGE_SIZE_ALL,
            filters: filters,
          });
          return res.data.map((item) => ({
            label: `${item.uom_name}`,
            value: item.name,
          }));
        },
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        width: 40,
      },
      {
        title: (
          <Tooltip
            color={COLOR_HEX.GREEN_TOOLTIP}
            key={COLOR_HEX.GREEN_TOOLTIP}
            title={intl.formatMessage({ id: 'Tỉ lệ = Đơn vị chuyển đổi / Đơn vị gốc' })}
          >
            <span>
              {intl.formatMessage({ id: 'category.material-management.conversion_factor' })}{' '}
            </span>
            <ExclamationCircleOutlined />
          </Tooltip>
        ),
        dataIndex: 'conversion_factor',
        valueType: 'digit',
        width: 40,
        fieldProps: {
          precision: 9,
        },
        render: (text) => <span>{text}</span>,
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      // {
      //   title: '题型',
      //   key: 'type',
      //   dataIndex: 'type',
      //   valueType: 'select',
      //   valueEnum: {
      //     multiple: { text: '多选题', status: 'Default' },
      //     radio: { text: '单选题', status: 'Warning' },
      //     vacant: {
      //       text: '填空题',
      //       status: 'Error',
      //     },
      //     judge: {
      //       text: '判断题',
      //       status: 'Success',
      //     },
      //   },
      // },
      {
        // title: intl.formatMessage({ id: 'common.action' }),
        valueType: 'option',
        width: 20,
        render: (text, record, _, action) => [
          <Button
            key="editable"
            icon={<EditOutlined />}
            onClick={() => {
              action?.startEditable?.(record.name);
            }}
            size="small"
          ></Button>,
          <Button
            key="delete"
            size="small"
            danger
            onClick={() => {
              const tableDataSource = form?.getFieldValue(nameKey) as DataSourceType[];
              form?.setFieldsValue({
                [nameKey]: tableDataSource.filter((item) => item.name !== record.name),
              });
            }}
            icon={<DeleteOutlined />}
          />,
        ],
      },
    ],
    [form],
  );
  return (
    <EditableProTable
      editableFormRef={editorFormRef}
      headerTitle={intl.formatMessage({ id: 'common.unit-conversion-table' })}
      rowKey={'name'}
      recordCreatorProps={{
        // newRecordType: 'dataSource',
        position: 'bottom',
        record: () => {
          return {
            name: nanoid(),
            doctype: 'UOM Conversion Detail',
            uom: undefined,
            conversion_factor: 0,
          };
        },
      }}
      editable={{
        type: 'multiple',
        actionRender: (row, config, defaultDom) => {
          return [
            // <Button
            //   key="save"
            //   onClick={() => {
            //     // config?.onSave?.(row.name);
            //     const originRow = row;
            //     config?.onSave?.(row, row);
            //   }}
            // >
            //   {formatMessage({ id: 'common.save' })}
            // </Button>,
            <a
              key="cancel"
              onClick={() => {
                config?.cancelEditable?.(row.name);
              }}
            >
              {formatMessage({ id: 'common.done' })}
            </a>,
          ];
        },
      }}
      name={nameKey}
      columns={columns}
    />
  );
};

export default UnitConversion;
