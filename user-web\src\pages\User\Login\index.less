.container {
  height: 100vh;

  .side {
    position: relative;
    background: #463839;

    .bg {
      width: 100%;
      height: 100%;
      object-fit: cover;
      max-height: 520px;
    }

    :global(.ant-carousel) {
      position: absolute;
      right: 5%;
      bottom: 5%;
      left: 5%;
      background: red;
    }
  }
}

.content {
  flex: 1;
  padding: 32px 0;

  :global(.ant-pro-form-login-header) {
    flex-direction: column;
    gap: 60px;
    height: unset;

    :global(.ant-pro-form-login-logo) {
      width: 298px;
      // height: 82px;
      margin-right: unset;
    }
  }
}

@media (min-width: @screen-md-min) {
  .container {
    background-color: white;
  }

  .content {
    padding: 32px 0 24px;
  }
}
