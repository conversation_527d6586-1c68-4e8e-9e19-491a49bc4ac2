import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { createCrop, updateCrop } from '@/services/cropManager';
import { createFarmingPlan, createFarmingPlanFromCopy } from '@/services/farming-plan';
import { uploadFile } from '@/services/fileUpload';
import { PageContainer, ProForm } from '@ant-design/pro-components';
import { FormattedMessage, history } from '@umijs/max';
import { App, Button, Space, UploadFile } from 'antd';
import { FC, ReactNode, useState } from 'react';
import DetailedInfo from './DetailedInfo';

interface CreateCropProps {
  children?: ReactNode;
  onSuccess?: () => void;
}
type IFormData = {
  label: string;
  zone_id: string;
  date_range: [string, string];
  square: number;
  plant_id: string;
  avatar?: UploadFile[];
  description?: string;
  status?: string;
  is_template?: boolean;
  quantity_estimate?: number;
  copy_plan_id: string;
  plan_label: string;
  plan_date_range: [string, string];
  img: any;
};
const CreateCrop: FC<CreateCropProps> = ({ onSuccess }) => {
  const { message } = App.useApp();
  const [submitting, setSubmitting] = useState(false);
  const onFinish = async (values: IFormData) => {
    setSubmitting(true);
    try {
      console.log('values,', values);
      // create
      const dataCreate = await createCrop({
        label: values.label,
        zone_id: values.zone_id,
        square: values.square,
        plant_id: values.plant_id,
        start_date: values.date_range[0],
        end_date: values.date_range[1],
        description: values.description,
        status: values.status,
        quantity_estimate: values.quantity_estimate,
        is_template: values.is_template,
      });

      //upload avatar
      if (values.avatar && (values.avatar || []).length > 0) {
        // kiểm tra các file đã upload

        const filesUploaded = values.avatar.filter((item: any) => !item.originFileObj);
        const filesNotUpload = values.avatar.filter((item: any) => item.originFileObj);
        // upload bất kể thành công hay ko
        const uploadListRes = await Promise.allSettled(
          filesNotUpload.map(async (item: any) => {
            return await uploadFile({
              docType: DOCTYPE_ERP.iotCrop,
              docName: dataCreate.data.name,
              file: item.originFileObj as any,
            });
          }),
        );
        // check if() 1 vài upload failed
        const checkUploadFailed = uploadListRes.find((item) => item.status === 'rejected');
        if (checkUploadFailed) {
          message.error({
            content: 'Some file upload failed',
          });
        }

        // update avatar path
        const arrFileUrl = uploadListRes
          .reduce<string[]>(
            (prev, item) =>
              item.status === 'fulfilled' ? [...prev, item?.value?.data?.message?.file_url] : prev,
            [],
          )
          .filter((item) => typeof item === 'string')
          // thêm file đã upload
          .concat(filesUploaded.map((item: any) => item.url as string));

        if (arrFileUrl.length > 0) {
          await updateCrop({
            name: dataCreate.data.name,
            avatar: arrFileUrl[0],
            zone_id: dataCreate.data.zone_id,
          });
        }
      }

      /**
       * CREATE PLAN
       */
      const cropId = dataCreate.data.name ? dataCreate.data.name : '';
      const plan_label = values.label; // tên kế hoạch trùng với tên vụ mùa
      if (cropId) {
        const copy_plan_id = values.copy_plan_id;
        let dataCreatePlan: any = [];

        if (copy_plan_id) {
          try {
            dataCreatePlan = await createFarmingPlanFromCopy({
              copy_plan_id: copy_plan_id,
              label: plan_label,
              crop: cropId,
              start_date: values.date_range[0],
              end_date: values.date_range[1],
            });
          } catch (error) {
            message.warning('Vụ mùa được tạo thành công nhưng kế hoạch chưa được tạo');
          }
        } else {
          dataCreatePlan = await createFarmingPlan({
            label: plan_label,
            crop: cropId,
            start_date: values.date_range[0],
            end_date: values.date_range[1],
          });
        }
        // upload file
        // if (values.img && (values.img || []).length > 0) {
        //   // upload bất kể thành công hay ko
        //   try {
        //     let fileObj: any = values.img[0].originFileObj;
        //     const dataUploadRes = await uploadFileHome({
        //       file: fileObj as any,
        //     });
        //     console.log('success upload', dataUploadRes);
        //     if (copy_plan_id) {
        //       await updateFarmingPlan({
        //         name: dataCreatePlan[0][0].name,
        //         crop: cropId,
        //         image: dataUploadRes.data.message.file_url,
        //       });
        //     } else {
        //       await updateFarmingPlan({
        //         name: dataCreatePlan.data.name,
        //         crop: cropId,
        //         image: dataUploadRes.data.message.file_url,
        //       });
        //     }
        //   } catch (error) {
        //     console.log('error', error);
        //     message.error({
        //       content: 'File upload failed',
        //     });
        //   }
        // }
      }

      message.success({
        content: 'Created successfully',
      });
      onSuccess?.();
      history.push('/farming-management/seasonal-management');
      return true;
    } catch (error) {
      // message.error({
      //   content: 'Error, please try again',
      // });
      return false;
    } finally {
      setSubmitting(false);
    }
  };
  const [form] = ProForm.useForm();
  return (
    <PageContainer
      fixedHeader
      // extra={[
      //   <Button key="cancel">Hủy</Button>,
      //   <Button key="save" type="primary">
      //     Lưu
      //   </Button>,
      // ]}
      footer={[
        <Button
          key="cancel"
          onClick={() => {
            history.back();
          }}
        >
          <FormattedMessage id="common.cancel" />
        </Button>,
        <Button
          loading={submitting}
          key="save"
          type="primary"
          onClick={() => {
            form.submit();
          }}
        >
          <FormattedMessage id="common.save" />
        </Button>,
      ]}
    >
      <ProForm<IFormData> onFinish={onFinish} submitter={false} form={form}>
        <Space
          size={'large'}
          direction="vertical"
          style={{
            width: '100%',
          }}
        >
          <DetailedInfo />
          {/* <TaskChild /> */}
          {/* <Card title="Vật tư liên quan">
            <ProFormSelect label="Chọn vật tư liên quan" />
          </Card> */}
          {/* <Card title="Hẹn giờ">
            <ProForm.Group>
              <ProFormTimePicker label="Chọn giờ" width={'lg'} />
              <ProFormDateRangePicker label={'Thời gian'} width={'lg'} />
            </ProForm.Group>
          </Card> */}
          {/* <SelectDevice /> */}
        </Space>
      </ProForm>
    </PageContainer>
  );
};

export default CreateCrop;
