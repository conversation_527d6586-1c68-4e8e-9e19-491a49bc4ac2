import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import FormTreeSelectSearch from '@/components/Form/FormTreeSelectSearch';
import { getItemGroupList, getItemList } from '@/services/stock/item';
import { getWarehouseList } from '@/services/stock/warehouse';
import { openInNewTab } from '@/services/utils';
import { IStockItem, IStockItemGroup } from '@/types/warehouse.type';
import { FileExcelOutlined, PrinterOutlined, SelectOutlined } from '@ant-design/icons';
import { ProForm, ProFormDatePicker, ProFormSelect } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Button, Card, Dropdown, Flex, Form, Select, Space } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import * as XLSX from 'xlsx';
import { useDateRangeStore } from '../../hooks/useDateRangeStore';
import { useSelectedWarehousedStore } from '../../hooks/useWarehouseStore';
import GeneralExportPrint from '../ExportPage/components/GeneralExportPrint';
import GeneralImportPrint from '../ExportPage/components/GeneralImportPrint';
import ImportExportPrint from '../ExportPage/components/ImportExportPrint';
import ImportExportValuePrint from '../ExportPage/components/ImportExportValuePrint';

interface Props {
  refreshIndicator: any;
}
interface ITreeNode {
  title: string;
  value: string;
  key: string;
  children?: ITreeNode[];
}
interface IFormData {
  warehouse: string;
  start_date: string;
  end_date: string;
  items: any;
}
const WarehouseReport = ({ refreshIndicator }: Props) => {
  const [treeData, setTreeData] = useState<ITreeNode[]>();
  const [form] = Form.useForm();
  const { selectedWarehouse } = useSelectedWarehousedStore();
  const { dateRange } = useDateRangeStore();
  const [type, setType] = useState<
    'importExport' | 'importExportValue' | 'generalImport' | 'generalExport'
  >('importExport');
  const [filter, setFilter] = useState<IFormData>();
  const [open, setOpen] = useState(true);
  const intl = useIntl();
  const [showItemSelector, setShowItemSelector] = useState(true);
  const [reportData, setReportData] = useState<any[]>([]); // New state to store report data

  //get list items in group
  const getItemTreeData = async () => {
    // Helper function
    function groupByItemGroupWithItemGroupData(
      stockItems: IStockItem[],
      stockItemGroups: IStockItemGroup[],
    ): Record<string, { items: IStockItem[]; itemGroup: IStockItemGroup }> {
      const groupedItems: Record<string, { items: IStockItem[]; itemGroup: IStockItemGroup }> = {};

      stockItems.forEach((item) => {
        const itemGroup = item.item_group;
        if (!groupedItems[itemGroup]) {
          groupedItems[itemGroup] = { items: [], itemGroup: {} as IStockItemGroup };
        }
        groupedItems[itemGroup].items.push(item);
      });

      // Now add item group data
      for (const itemGroup of stockItemGroups) {
        const itemGroupName = itemGroup.item_group_name;
        if (groupedItems[itemGroupName]) {
          groupedItems[itemGroupName].itemGroup = itemGroup;
        }
      }

      return groupedItems;
    }
    const data = await getItemList({});
    const dataGroup = await getItemGroupList({});
    const dataMap = groupByItemGroupWithItemGroupData(data.data, dataGroup.data);
    if (dataMap) {
      const generatedData = Object.entries(dataMap).map(([itemGroup, groupData]) => ({
        title: groupData.itemGroup?.label || '',
        value: itemGroup,
        key: itemGroup,
        children: groupData.items.map((item) => ({
          title: item.label || '',
          value: item.name || '',
          key: item.name || '',
        })),
      }));
      setTreeData(generatedData);
    }
  };

  const handleFinish = async (e: any) => {
    console.log(e);
    setFilter({
      ...form.getFieldsValue(),
      start_date: dayjs(form.getFieldValue('start_date')).format('YYYY-MM-DD'),
      end_date: dayjs(form.getFieldValue('end_date')).format('YYYY-MM-DD'),
    });
    setOpen(false);
    return true;
  };

  const formatBeforeExportExcel = (data: any) => {
    switch (type) {
      case 'importExport':
        return data.map((item: any) => ({
          'Mã hàng': item.item_code,
          'Tên hàng': item.item_label,
          'Nhóm hàng': item.item_group_label,
          'Đơn vị': item.uom_name,
          Kho: item.set_warehouse_label,
          'Tồn đầu': item.begin_qty,
          Nhập: item.import_qty,
          Xuất: item.export_qty,
          'Tồn cuối': item.end_qty,
        }));
      case 'importExportValue':
        return data.map((item: any) => ({
          'Mã hàng': item.item_code,
          'Tên hàng': item.item_label,
          'Nhóm hàng': item.item_group_label,
          'Đơn vị': item.uom_name,
          Kho: item.set_warehouse_label,
          'Giá trị tồn đầu': item.begin_price,
          'Giá trị nhập': item.import_price,
          'Giá trị xuất': item.export_price,
          'Giá trị tồn cuối': item.end_price,
        }));
      case 'generalExport':
        return data.map((item: any) => ({
          'Mã phiếu': item.name,
          'Ngày tạo': item.creation,
          'Nhà cung cấp': item.supplier_label,
          'Tổng giá (VNĐ)': item.rounded_total,
          'Người tạo': `${item.user_first_name} ${item.user_last_name}`,
          Kho: item.set_warehouse_label,
        }));
      case 'generalImport':
        return data.map((item: any) => ({
          'Mã phiếu': item.name,
          'Ngày tạo': item.creation,
          'Nhà cung cấp': item.supplier_label,
          'Tổng giá (VNĐ)': item.rounded_total,
          'Người tạo': `${item.user_first_name} ${item.user_last_name}`,
          Kho: item.set_warehouse_label,
        }));
      default:
        return data;
    }
  };

  const exportToExcel = () => {
    if (!reportData.length) return;

    const formattedData = formatBeforeExportExcel(reportData);
    const worksheet = XLSX.utils.json_to_sheet(formattedData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Report');

    XLSX.writeFile(workbook, 'WarehouseReport.xlsx');
  };

  useEffect(() => {
    const fetchData = async () => {
      getItemTreeData();
    };
    fetchData();
  }, [refreshIndicator]);

  //debug reportData
  useEffect(() => {
    console.log('fi');
    console.log({ reportData });
  }, [reportData]);
  const handleDataLoaded = useCallback((data: any[]) => {
    // setReportData(data);
  }, []);
  return (
    <>
      <Card>
        <Flex justify="space-between">
          <Space size={'middle'}>
            <FormattedMessage id={'common.report'} />
            <Select
              defaultValue={'importExport'}
              options={[
                {
                  value: 'importExport',
                  label: <FormattedMessage id={'common.import_export_report'} />,
                },
                {
                  value: 'importExportValue',
                  label: <FormattedMessage id={'common.import_export_report_value'} />,
                },
                {
                  value: 'generalImport',
                  label: <FormattedMessage id={'common.general_import_report'} />,
                },
                {
                  value: 'generalExport',
                  label: <FormattedMessage id={'common.general_export_report'} />,
                },
              ]}
              onSelect={(e: any) => {
                const filteredType = ['generalImport', 'generalExport'];
                setShowItemSelector(!filteredType.includes(e));
                setType(e);
              }}
            />
            <Dropdown
              menu={{ items: [] }}
              open={open}
              dropdownRender={(menu) => (
                <Card
                  size="small"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                >
                  <ProForm
                    form={form}
                    grid
                    size="small"
                    autoFocusFirstInput
                    style={{
                      width: '30vh',
                    }}
                    onFinish={handleFinish}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    initialValues={{
                      warehouse: selectedWarehouse,
                      start_date: dateRange?.at(0),
                      end_date: dateRange?.at(1),
                    }}
                    submitter={{
                      render: (props, defaultDoms) => {
                        return [
                          <Button
                            key="reset"
                            onClick={() => {
                              props.reset();
                            }}
                            style={{ width: '30%' }}
                          >
                            Hủy
                          </Button>,
                          <Button
                            key="ok"
                            onClick={() => {
                              props.submit();
                            }}
                            type="primary"
                            style={{ width: '30%' }}
                          >
                            Đồng ý
                          </Button>,
                        ];
                      },
                    }}
                  >
                    <ProFormSelect
                      showSearch
                      name="warehouse"
                      rules={[{ required: true, message: 'Vui lòng chọn kho' }]}
                      required
                      label={<FormattedMessage id="warehouse-management.warehouse-name" />}
                      colProps={{ span: 24 }}
                      request={async () => {
                        const warehouse = await getWarehouseList();
                        return [
                          { label: <FormattedMessage id={'common.all'} />, value: 'all' },
                          ...warehouse.data.map((storage) => ({
                            label: storage.label,
                            value: storage.name,
                          })),
                        ];
                      }}
                      width={'md'}
                    />
                    {showItemSelector && (
                      <FormTreeSelectSearch
                        name={'items'}
                        fieldProps={{
                          treeData: treeData,
                        }}
                        label={
                          <FormattedMessage id={'warehouse-management.import-voucher.item_name'} />
                        }
                        colProps={{ span: 24 }}
                        width={'md'}
                        placeholder={intl.formatMessage({ id: 'common.all' })}
                      />
                    )}
                    <ProFormDatePicker
                      name="start_date"
                      label={<FormattedMessage id={'common.start_date'} />}
                      colProps={{ span: 12 }}
                      fieldProps={{
                        format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
                      }}
                    />
                    <ProFormDatePicker
                      name="end_date"
                      label={<FormattedMessage id={'common.end_date'} />}
                      colProps={{ span: 12 }}
                      fieldProps={{
                        format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
                      }}
                    />
                  </ProForm>
                </Card>
              )}
              trigger={['hover']}
            >
              <Button
                type="primary"
                onClick={() => {
                  setOpen(!open);
                }}
              >
                <Space>
                  Chọn dữ liệu
                  <SelectOutlined />
                </Space>
              </Button>
            </Dropdown>
          </Space>
          {filter && (
            <Space>
              <Button
                key={'download'}
                icon={<PrinterOutlined />}
                onClick={() =>
                  openInNewTab(
                    `/warehouse-management-v3/to-pdf?type=${type}&warehouse=${
                      filter.warehouse
                    }&start_date=${filter.start_date}&end_date=${
                      filter.end_date
                    }&item_code_list=${JSON.stringify(filter.items ?? [])}`,
                  )
                }
                type="primary"
              >
                {' '}
                {<FormattedMessage id={'common.print_receipt'} />}
              </Button>
              <Button
                key={'export'}
                icon={<FileExcelOutlined />}
                onClick={exportToExcel}
                type="primary"
              >
                {' '}
                {<FormattedMessage id={'common.export_excel'} />}
              </Button>
            </Space>
          )}
        </Flex>
      </Card>
      <Card>
        {filter && (
          <>
            {type === 'importExport' && (
              <ImportExportPrint
                warehouse={filter.warehouse}
                end_date={filter.end_date}
                start_date={filter.start_date}
                item_code_list={filter.items || []}
                onDataLoaded={handleDataLoaded}
              />
            )}
            {type === 'importExportValue' && (
              <ImportExportValuePrint
                warehouse={filter.warehouse}
                end_date={filter.end_date}
                start_date={filter.start_date}
                item_code_list={filter.items || []}
                // onDataLoaded={handleDataLoaded}
              />
            )}
            {type === 'generalImport' && (
              <GeneralImportPrint
                warehouse={filter.warehouse}
                end_date={filter.end_date}
                start_date={filter.start_date}
                // onDataLoaded={handleDataLoaded}
              />
            )}
            {type === 'generalExport' && (
              <GeneralExportPrint
                warehouse={filter.warehouse}
                end_date={filter.end_date}
                start_date={filter.start_date}
                // onDataLoaded={handleDataLoaded}
              />
            )}
            {type !== 'importExport' &&
              type !== 'importExportValue' &&
              type !== 'generalImport' &&
              type !== 'generalExport' &&
              'Invalid report type'}
          </>
        )}
      </Card>
    </>
  );
};

export default WarehouseReport;
