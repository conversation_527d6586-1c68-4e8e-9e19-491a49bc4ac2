import loginBg from '@/assets/img/login-bg.png';
import logoTextWhite from '@/assets/img/logo-text-white.svg';
import Footer from '@/components/Footer';
import { login } from '@/services/auth';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { LoginForm, ProFormText } from '@ant-design/pro-components';
import { Link, useModel } from '@umijs/max';
import { Col, message, Row } from 'antd';
import { createStyles } from 'antd-use-styles';
import React, { useEffect } from 'react';
import styles from './index.less';

const useStyles = createStyles(({ token }) => ({
  slideFooter: {
    color: 'white',
  },
  textLarge: {
    fontSize: token.fontSizeHeading1 + 10,
    fontWeight: 700,
  },
  textMedium: {
    fontSize: token.fontSizeHeading3,
    fontWeight: token.fontWeightStrong,
  },
  text: {
    fontSize: token.fontSizeHeading4,
    fontWeight: token.fontWeightStrong,
    lineHeight: 1.5,
  },
  textUppercase: {
    textTransform: 'uppercase',
  },
}));

const Login: React.FC = () => {
  const style = useStyles();
  const { initialState, setInitialState } = useModel('@@initialState');

  const fetchUserInfo = async (onSuccess?: any) => {
    const userInfo = await initialState?.fetchUserInfo?.();
    if (userInfo) {
      await setInitialState((s: any) => ({ ...s, currentUser: userInfo }));
      onSuccess?.();
    }
  };

  const handleSubmit = async (values: API.RequestLogin) => {
    try {
      // 登录
      const loginResp = await login({ ...values });
      const userdata = loginResp?.result?.user;
      // console.log(userdata, "userdata");
      localStorage.setItem('token', JSON.stringify(loginResp?.result));
      await setInitialState((s: any) => ({ ...s, currentUser: userdata }));
      window.location.href = window.location.origin;
    } catch (error) {
      message.error('Login Failed, Try Again!');
    }
  };

  useEffect(() => {
    if (!initialState?.currentUser?.user_id) fetchUserInfo();
    const urlParams = new URL(window.location.href).searchParams;
    // history.push(urlParams.get('redirect') || '/');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialState?.currentUser?.user_id]);
  const mainternanceMode = initialState?.maintenanceMode;
  return (
    <Row className={styles.container}>
      <Col md={12} xs={0} className={styles.side}>
        <div
          style={{
            paddingBlock: 45,
            paddingInline: 30,
          }}
        >
          <img src={logoTextWhite} />
        </div>
        <img className={styles.bg} src={loginBg} alt="bg" />
        <div
          className={style.slideFooter}
          style={{
            paddingBlock: 45,
            paddingInline: 30,
          }}
        >
          <Row>
            <Col span={13}>
              <div className={style.textUppercase}>
                <div className={style.textLarge}>Quản lý </div>
                <div
                  className={style.textLarge}
                  style={{
                    marginBlockEnd: 10,
                  }}
                >
                  thông minh
                </div>
                <div className={style.textMedium}>Trong nông nghiệp</div>
              </div>
            </Col>
            <Col span={9}>
              <div
                className={style.text}
                style={{
                  textAlign: 'right',
                }}
              >
                Với VIIS bạn có thể dễ dàng điều khiển, nhanh chóng, tiết kiệm thời gian và công
                sức. Quản lý công việc dễ dàng và hiệu quả.
              </div>
            </Col>
          </Row>
        </div>
      </Col>
      <Col md={12} xs={24}>
        <div className={styles.content}>
          <LoginForm
            logo={<img style={{ width: '150px' }} alt="logo" src="/viis_logo.svg" />}
            title={<div style={{ marginTop: '20px' }}>ĐĂNG NHẬP VÀO HỆ THỐNG</div>}
            subTitle={
              <>
                {/* <p>Professional English training partner for many enterprises and large</p>
                <p>multinational corporations in Vietnam</p> */}
              </>
            }
            initialValues={{
              autoLogin: true,
            }}
            onFinish={async (values) => {
              await handleSubmit(values as API.RequestLogin);
            }}
            submitter={{
              searchConfig: {
                submitText: mainternanceMode ? 'Đang bảo trì' : 'Đăng nhập',
              },
            }}
          >
            <ProFormText
              name="usr"
              fieldProps={{
                size: 'large',
                prefix: <UserOutlined className={styles.prefixIcon} />,
              }}
              placeholder={'Email'}
              rules={[
                {
                  required: true,
                  message: 'field required',
                },
              ]}
            />
            <ProFormText.Password
              name="pwd"
              fieldProps={{
                size: 'large',
                prefix: <LockOutlined className={styles.prefixIcon} />,
              }}
              placeholder={'Mật khẩu'}
              rules={[
                {
                  required: true,
                  message: 'field required',
                },
              ]}
            />

            <div
              style={{
                marginBottom: 24,
              }}
            >
              <div
                style={{
                  float: 'right',
                }}
              >
                <Link to={'/user/forgot-password'}>Bạn quên mật khẩu?</Link>
              </div>
            </div>
          </LoginForm>
        </div>
        <Footer />
      </Col>
    </Row>
  );
};

export default Login;
