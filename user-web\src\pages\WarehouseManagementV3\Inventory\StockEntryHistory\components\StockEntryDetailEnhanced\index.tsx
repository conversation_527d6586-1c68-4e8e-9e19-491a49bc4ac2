import ConfirmModal from '@/components/ConfirmMdal';
import DocStatusTag from '@/components/DocStatusTag';
import { getStockEntryNoteDetail } from '@/services/stock/stockEntry';
import { openInNewTab } from '@/services/utils';
import { PrinterOutlined } from '@ant-design/icons';
import { ModalForm } from '@ant-design/pro-components';
import { FormattedMessage, useRequest } from '@umijs/max';
import { Button, Form, Spin } from 'antd';
import { FC, useEffect, useMemo, useState } from 'react';
import ActionButton from '../../../components/StockActionButton';
import { voucherActionConfigs } from '../../../components/StockActionButton/voucherActions';
import { StockEntryForm } from './components/StockEntryForm';
import { useModalState } from './hooks/useMainModalState';
import { useStockEntryLogic } from './hooks/useStockEntryLogic';
import { useStockEntryStore } from './stores/useStockEntryStore';
import { ModalAction } from './types';

interface Props {
  name: string;
  onSuccess?: () => void;
  isModalOpen: boolean;
  setIsModalOpen: (value: boolean) => void;
}

const StockEntryDetailEnhanced: FC<Props> = ({ name, onSuccess, isModalOpen, setIsModalOpen }) => {
  const { handleSave, handleSubmit, handleCancel, handleDelete } = useStockEntryLogic(onSuccess);
  const store = useStockEntryStore();
  const [form] = Form.useForm();
  const { modalStates, showModal, hideModal } = useModalState();
  const [selectedActionComponent, setSelectedActionComponent] = useState<JSX.Element | null>(null);

  // Handler for action button selection
  const handleActionSelect = (Component: FC<any>, initialData: any) => {
    setIsModalOpen(false); // Close current modal
    setSelectedActionComponent(
      <Component
        onSuccess={() => {
          onSuccess?.();
          setSelectedActionComponent(null); // Clear after success
        }}
        onClose={() => setSelectedActionComponent(null)}
        initialData={initialData}
        autoOpen={true}
      />,
    );
  };

  const { data, run } = useRequest(getStockEntryNoteDetail, {
    manual: true,
    onError(error) {
      console.error('Error fetching stock entry:', error.message);
    },
    async onSuccess(data) {
      console.log('fetched data is', data);
      form.setFieldsValue({
        name: data.name,
        posting_date: data.posting_date,
        purpose: data.purpose,
        employee: `${data.user_first_name} ${data.user_last_name}`,
        description: data.description,
        source_warehouse: data.items?.[0]?.s_warehouse,
        target_warehouse: data.items?.[0]?.t_warehouse,
        file_path: data.file_path,
      });
      store.setSavedVoucherData(data);
      store.setDocstatus(data.docstatus);
      store.setSelectedSourceWarehouse(data.items?.[0]?.s_warehouse || '');
      store.setSelectedTargetWarehouse(data.items?.[0]?.t_warehouse || '');
      if (data.items) {
        const transformedItems = data.items.map((item: any) => ({
          item_code: item.item_code,
          item_name: item.item_name,
          item_label: item.item_label,
          actual_qty: item.actual_qty,
          qty: item.qty,
          rate: item.valuation_rate,
          total_price: item.amount,
          warehouse: item.s_warehouse || item.t_warehouse,
          uom: item.uom || '',
          uom_label: item.uom_name || 'N/A',
          name: item.name,
          conversion_factor: item.conversion_factor || 1,
        }));
        console.log('transformedItems', transformedItems);
        store.setSelectedItems(transformedItems);
      }
    },
  });

  useEffect(() => {
    store.setForm(form);
  }, [store.form]);

  useEffect(() => {
    if (isModalOpen) {
      run({ name });
    }
  }, [name, isModalOpen]);

  useEffect(() => {
    if (isModalOpen && store.selectedSourceWarehouse && !store.isItemTreeDataLoaded) {
      store.fetchItemTreeData(store.selectedSourceWarehouse);
    }
  }, [isModalOpen, store.selectedSourceWarehouse, store.isItemTreeDataLoaded]);

  const handleActionSuccess = async (action: () => Promise<any>) => {
    try {
      await action();
      setIsModalOpen(false);
      onSuccess?.();
    } catch (error) {
      console.error('Action failed:', error);
    }
  };

  const modalConfig = useMemo(
    () => ({
      [ModalAction.SUBMIT]: {
        title: 'Hoàn thành phiếu',
        message: 'Bạn có chắc chắn muốn hoàn thành phiếu?',
        description: 'Sau khi hoàn thành, phiếu sẽ không thể chỉnh sửa.',
        action: () => handleActionSuccess(handleSubmit),
      },
      [ModalAction.CANCEL]: {
        title: 'Hủy phiếu',
        message: 'Bạn có chắc chắn muốn hủy phiếu?',
        description: 'Sau khi hủy, phiếu sẽ không thể khôi phục.',
        type: 'danger' as const,
        action: () => handleActionSuccess(handleCancel),
      },
      [ModalAction.DELETE]: {
        title: 'Xóa phiếu',
        message: 'Bạn có chắc chắn muốn xóa phiếu?',
        description: 'Phiếu sẽ bị xóa vĩnh viễn và không thể khôi phục.',
        type: 'danger' as const,
        action: () => handleActionSuccess(handleDelete),
      },
    }),
    [handleSubmit, handleCancel, handleDelete],
  );

  const renderActionButtons = () => {
    const isEditable = store.docstatus === 0; // Draft
    const isSubmitted = store.docstatus === 1; // Submitted
    const stockEntryType = store.savedVoucherData?.purpose || 'Material Issue';

    let receiptType = '';
    let voucherActionType = '';

    switch (stockEntryType) {
      case 'Material Issue':
        receiptType = 'materialIssue';
        voucherActionType = 'Material Issue';
        break;
      case 'Material Receipt':
        receiptType = 'materialReceipt';
        voucherActionType = 'Material Receipt';
        break;
      case 'Material Transfer':
        receiptType = 'materialTransfer';
        voucherActionType = 'Material Transfer';
        break;
      default:
        receiptType = 'notHandledType';
        voucherActionType = 'Material Issue';
    }

    return (
      <div
        style={{
          paddingLeft: '25.5rem',
          paddingRight: '22rem',
          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
          alignItems: 'center',
        }}
      >
        <div style={{ display: 'flex', gap: '8px' }}>
          {isEditable && (
            <>
              <Button
                key="save"
                type="primary"
                onClick={() =>
                  handleActionSuccess(async () => {
                    const values = await form.validateFields();
                    await handleSave(values);
                  })
                }
                loading={store.submitting}
                style={{ width: '150px' }}
              >
                <FormattedMessage id="common.save" />
              </Button>
              <Button
                key="submit"
                type="primary"
                onClick={() => showModal(ModalAction.SUBMIT)}
                loading={store.submitting}
                style={{ width: '150px' }}
              >
                <FormattedMessage id="common.submit" />
              </Button>
              <Button
                key="delete"
                danger
                onClick={() => showModal(ModalAction.DELETE)}
                loading={store.submitting}
                style={{ width: '150px' }}
              >
                <FormattedMessage id="common.delete" />
              </Button>
            </>
          )}
          {isSubmitted && (
            <Button
              key="cancel"
              danger
              onClick={() => {
                console.log('Cancel button clicked');
                showModal(ModalAction.CANCEL);
              }}
              disabled={store.submitting}
              style={{ width: '150px' }}
            >
              <FormattedMessage id="common.cancel" />
            </Button>
          )}
          {/* Nút In phiếu luôn hiển thị và không bị disabled */}
          <Button
            key="print"
            icon={<PrinterOutlined />}
            onClick={() => {
              openInNewTab(`/warehouse-management-v3/to-pdf?type=${receiptType}&id=${data?.name}`);
            }}
            disabled={false} // Rõ ràng không bao giờ disabled
            style={{ width: '150px' }}
          >
            <FormattedMessage id="common.print_receipt" />
          </Button>
        </div>

        {/* Only show ActionButton for submitted documents */}
        {isSubmitted &&
          voucherActionConfigs[voucherActionType as keyof typeof voucherActionConfigs] && (
            <ActionButton
              voucherData={store.savedVoucherData}
              actions={voucherActionConfigs[
                voucherActionType as keyof typeof voucherActionConfigs
              ].map((action) => ({
                ...action,
                onSelect: () =>
                  handleActionSelect(
                    action.createComponent,
                    action.mapData(store.savedVoucherData),
                  ),
              }))}
              onActionSuccess={onSuccess}
              closeCurrentModal={() => {
                console.log('Closing current modal in StockEntryDetailEnhanced');
                setIsModalOpen(false);
              }}
            />
          )}
      </div>
    );
  };

  const getTitle = () => {
    const purpose = store.savedVoucherData?.purpose || 'Material Issue';
    let titleId = 'warehouse-management.material-issue';
    if (purpose === 'Material Receipt') {
      titleId = 'warehouse-management.material-receipt';
    } else if (purpose === 'Material Transfer') {
      titleId = 'warehouse-management.material-transfer';
    }
    return (
      <>
        <FormattedMessage id={titleId} /> {store.savedVoucherData?.name || ''} {' - '}{' '}
        <DocStatusTag status={store.docstatus} />
      </>
    );
  };

  return (
    <>
      <ModalForm
        disabled={store.docstatus !== 0} // Vô hiệu hóa form nhưng không ảnh hưởng đến nút Print
        open={isModalOpen}
        title={getTitle()}
        modalProps={{
          onCancel: () => {
            store.reset();
            setIsModalOpen(false);
          },
          destroyOnClose: true,
        }}
        width={1600}
        form={form}
        layout="vertical"
        rowProps={{ gutter: [16, 0] }}
        submitter={{
          render: renderActionButtons,
        }}
        autoFocusFirstInput
        grid
      >
        <Spin spinning={store.submitting}>
          <StockEntryForm />
        </Spin>
        {Object.entries(modalConfig).map(([action, config]) => (
          <ConfirmModal
            key={action}
            open={modalStates[action as ModalAction]}
            {...config}
            onConfirm={async () => {
              hideModal(action as ModalAction);
              await config.action();
            }}
            onCancel={() => hideModal(action as ModalAction)}
            confirmLoading={store.submitting}
            maskClosable={false}
          />
        ))}
      </ModalForm>
      {selectedActionComponent}
    </>
  );
};

export default StockEntryDetailEnhanced;
