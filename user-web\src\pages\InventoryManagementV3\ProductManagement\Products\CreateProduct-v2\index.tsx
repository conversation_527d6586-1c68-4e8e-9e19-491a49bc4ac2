/* eslint-disable no-useless-escape */
import { ProForm } from '@ant-design/pro-components';
import { App, Button, Tabs } from 'antd';
import { FC } from 'react';

import withTriggerFormModal, { TriggerFormModalProps } from '@/HOC/withTriggerFormModal';
import { PlusOutlined } from '@ant-design/icons';
import { ModalForm } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { useCreateProductItemV3 } from '../hooks/useCreateProductItemV3';
import InfoTab from './components/InforTab';

type AddNewItemProps = TriggerFormModalProps<{
  defaultValue?: any;
  id?: string;
}>;
const ContentForm: FC<AddNewItemProps> = ({
  open,
  onOpenChange,
  onSuccess,
  modalProps,
  trigger,
}) => {
  const { formatMessage } = useIntl();
  const { message } = App.useApp();
  const [form] = ProForm.useForm();
  const { run: createProductItemV3 } = useCreateProductItemV3({
    onSuccess,
  });
  const intl = useIntl();
  return (
    <ModalForm
      form={form}
      title={<FormattedMessage id={'category.material-management.add_category'} />}
      name="category.material-management.add_category"
      width={800}
      onFinish={async (values) => {
        const data: Parameters<typeof createProductItemV3>[0] = {
          image: values.image,
          stock_uom: values.stock_uom,
          standard_rate: values.standard_rate, //gia ban mac dinh
          label: values.label,
          item_name: values.item_name,
          // "iot_customer": "09cb98f0-e0f5-11ec-b13b-4376e531a14a", //optional
          item_group: values.item_group,
          valuation_rate: values.valuation_rate, //gia mua mac dinh
          uoms: values.uoms,
          description: values.description,
          max_stock_level: values.max_stock_level,
          min_stock_level: values.min_stock_level,
        };
        await createProductItemV3(data);
        onSuccess?.();
        return true;
      }}
      open={open}
      onOpenChange={onOpenChange}
      trigger={trigger}
    >
      <Tabs defaultActiveKey="1">
        <Tabs.TabPane tab={intl.formatMessage({ id: 'common.info' })} key="1">
          <InfoTab form={form} />
        </Tabs.TabPane>
        <Tabs.TabPane tab={intl.formatMessage({ id: 'common.attribute' })} key="2">
          {/* Content for Thuộc tính tab */}
        </Tabs.TabPane>
        <Tabs.TabPane tab={intl.formatMessage({ id: 'common.supplier' })} key="3">
          {/* Content for Nhà cung cấp tab */}
        </Tabs.TabPane>
        <Tabs.TabPane tab={intl.formatMessage({ id: 'common.customer' })} key="4">
          {/* Content for Khách hàng tab */}
        </Tabs.TabPane>
        <Tabs.TabPane tab={intl.formatMessage({ id: 'common.inventory-voucher' })} key="5">
          {/* Content for Thẻ kho tab */}
        </Tabs.TabPane>
      </Tabs>
    </ModalForm>
  );
};

const CreateProduct: FC<any> = ({ buttonType = 'primary', ...props }) => {
  const CreateProductWithTrigger = withTriggerFormModal({
    defaultTrigger: ({ changeOpen, disabled }) => (
      // <Button
      //   type="primary"
      //   disabled={disabled}
      //   icon={<PlusOutlined />}
      //   onClick={() => changeOpen(true)}
      // >
      //   <FormattedMessage id={'category.material-management.add_category'} />
      // </Button>
      <>
        {buttonType === 'primary' ? (
          <Button
            disabled={disabled}
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              console.log('clicked primary');
              return changeOpen(true);
            }}
          >
            <FormattedMessage id="category.material-management.add_category" />
          </Button>
        ) : (
          <Button
            type="link"
            style={{ flex: 'none', padding: '8px', display: 'block', cursor: 'pointer' }}
            onClick={() => {
              console.log('clicked link');
              return changeOpen(true);
            }}
          >
            <PlusOutlined color="primary" />{' '}
            <FormattedMessage id="category.material-management.add_category" />
          </Button>
        )}
      </>
    ),
    contentRender: (props) => <ContentForm {...props} />,
  });
  return <CreateProductWithTrigger />;
};

const CreateProductWithTrigger = withTriggerFormModal({
  defaultTrigger: ({ changeOpen, disabled, buttonType = 'primary' }) => (
    <>
      {buttonType === 'primary' ? (
        <Button
          disabled={disabled}
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            console.log('clicked primary');
            return changeOpen(true);
          }}
        >
          <FormattedMessage id="category.material-management.add_category" />
        </Button>
      ) : (
        <Button
          type="link"
          className="flex items-center gap-1"
          style={{
            padding: '8px',
            display: 'flex',
            alignItems: 'center',
            color: '#44c4a1', // màu xanh lá của Ant Design
            cursor: 'pointer',
          }}
          onClick={() => {
            console.log('clicked link');
            return changeOpen(true);
          }}
        >
          <PlusOutlined style={{ color: '#44c4a1' }} />
          <span>
            <FormattedMessage id="category.material-management.add_category" />
          </span>
        </Button>
      )}
    </>
  ),
  contentRender: ContentForm,
});
export default CreateProductWithTrigger;
