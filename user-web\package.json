{"name": "ant-design-pro", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "clean-s3": "aws s3 rm --recursive s3://smartfarm.viis.tech", "clean-s3-demo": "aws s3 rm --recursive s3://smartstock.viis.tech", "clean-s3-stage": "aws s3 rm --recursive s3://stage-smartfarm.viis.tech", "clear-cache": "aws cloudfront create-invalidation --distribution-id E2UBLTTDP24DVY --paths \"/*\"", "deploy": "npm run build && npm run gh-pages", "deploy-prod": "max build && aws s3 rm --recursive s3://smartfarm.viis.tech && aws s3 sync dist/ s3://smartfarm.viis.tech && yarn clear-cache", "deploy-s3": "aws s3 sync dist/ s3://smartfarm.viis.tech", "deploy-s3-demo": "aws s3 sync dist/ s3://smartstock.viis.tech", "deploy-s3-stage": "max build && aws s3 rm --recursive s3://stage-smartfarm.viis.tech && aws s3 sync dist/ s3://stage-smartfarm.viis.tech", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "cd .. && husky install admin-lms-app/.husky", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:local-api": "cross-env UMI_ENV=dev LOCAL_API=true max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.7.0", "@ant-design/pro-components": "^2.3.37", "@ant-design/use-emotion-css": "1.0.4", "@fullcalendar/bootstrap5": "^6.1.8", "@fullcalendar/core": "^6.0.3", "@fullcalendar/daygrid": "^6.0.3", "@fullcalendar/interaction": "^6.0.3", "@fullcalendar/multimonth": "^6.1.8", "@fullcalendar/react": "^6.0.4", "@fullcalendar/timegrid": "^6.0.3", "@react-google-maps/api": "^2.19.3", "@rematch/core": "^2.2.0", "@types/bcrypt": "^5.0.0", "@types/google-map-react": "^2.1.8", "@types/numeral": "^2.0.3", "@umijs/plugin-model": "^2.6.2", "@umijs/route-utils": "^2.1.3", "ahooks": "^3.7.8", "antd": "5.x", "antd-img-crop": "^4.12.2", "antd-use-styles": "^1.1.4", "apexcharts": "4.0.0", "bcryptjs": "^2.4.3", "bootstrap": "5.2.3", "bootstrap-icons": "^1.10.3", "chart.js": "^4.2.1", "chartjs-adapter-moment": "^1.0.1", "chartjs-plugin-annotation": "^3.0.1", "chartjs-plugin-zoom": "^2.0.0", "classnames": "^2.3.2", "date-fns": "^2.29.3", "dayjs": "^1.11.10", "dotenv": "^16.4.7", "eventemitter3": "^5.0.1", "file-saver": "^2.0.5", "frappe-gantt-react": "^0.2.2", "fullcalendar": "^6.1.8", "google-map-react": "^2.2.1", "hammerjs": "^2.0.8", "highcharts": "^10.3.3", "highcharts-react-official": "^3.1.0", "html2canvas": "^1.4.1", "http-server": "^14.1.1", "jspdf": "^2.5.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "lunar-calendar": "^0.1.4", "mapbox-gl": "^2.12.0", "moment": "^2.29.4", "mqtt": "^4.3.7", "nanoid": "^4.0.2", "numeral": "^2.0.6", "omit.js": "^2.0.2", "prop-types": "^15.8.1", "query-string": "^8.1.0", "rc-menu": "^9.6.4", "rc-util": "^5.24.4", "react": "^18.0.0", "react-apexcharts": "^1.4.1", "react-barcode": "^1.4.6", "react-big-calendar": "^1.6.3", "react-chartjs-2": "^5.2.0", "react-countup": "^6.4.1", "react-dev-inspector": "^1.8.1", "react-dom": "^18.0.0", "react-helmet-async": "^1.3.0", "react-highlight-words": "^0.20.0", "react-icons": "^4.12.0", "react-infinite-scroll-component": "^6.1.0", "react-merge-refs": "^2.0.2", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^6.8.0", "react-to-print": "^2.14.13", "react-virtuoso": "^4.6.2", "reselect": "^4.1.7", "unorm": "^1.6.0", "utc": "^0.1.0", "uuid": "^9.0.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.19.2/xlsx-0.19.2.tgz", "zustand": "^4.5.2"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.0", "@faker-js/faker": "^8.0.2", "@testing-library/react": "^13.4.0", "@types/bcryptjs": "^2.4.6", "@types/classnames": "^2.3.1", "@types/express": "^4.17.14", "@types/file-saver": "^2.0.5", "@types/history": "^4.7.11", "@types/jest": "^29.2.1", "@types/lodash": "^4.14.186", "@types/react": "^18.0.0", "@types/react-big-calendar": "^1.6.0", "@types/react-dom": "^18.0.0", "@types/react-helmet": "^6.1.5", "@types/react-highlight-words": "^0.20.0", "@types/unorm": "^1.3.31", "@types/uuid": "^9.0.1", "@umijs/lint": "^4.0.34", "@umijs/max": "^4.0.34", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "eslint": "^8.0.0", "eslint-plugin-react-hooks": "^5.2.0", "express": "^4.18.2", "gh-pages": "^3.2.0", "husky": "^7.0.4", "jest": "^29.2.2", "jest-environment-jsdom": "^29.2.2", "lint-staged": "^10.0.0", "mockjs": "^1.1.0", "postcss": "^8.4.38", "prettier": "^2.7.1", "swagger-ui-dist": "^4.14.2", "tailwindcss": "^3.4.3", "ts-node": "^10.9.1", "typescript": "^4.8.4", "umi-presets-pro": "^2.0.0"}, "engines": {"node": ">=12.0.0"}}