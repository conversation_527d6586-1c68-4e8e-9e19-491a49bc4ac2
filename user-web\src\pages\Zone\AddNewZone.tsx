import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import useFormAddress from '@/components/Form/FormAddress';
import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import withTriggerFormModal, { TriggerFormModalProps } from '@/HOC/withTriggerFormModal';
import { getProjectList } from '@/services/project';
import { createZone } from '@/services/zoneManager';
import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { App, Button, Col, InputNumber, Row } from 'antd';
import { FC, useEffect } from 'react';

type AddNewZoneInProjectProps = TriggerFormModalProps<{
  defaultValue?: any;
}>;

const ContentForm: FC<AddNewZoneInProjectProps> = ({
  open,
  onOpenChange,
  onSuccess,
  modalProps,
  trigger,
}) => {
  const { formatMessage } = useIntl();
  const [form] = ProForm.useForm();
  const { message } = App.useApp();
  useEffect(() => {
    try {
      if (modalProps?.defaultValue) {
        form.setFieldsValue(modalProps.defaultValue);
      }
    } catch (error) {}
  }, [modalProps?.defaultValue]);
  const { districtElement, cityElement, wardElement, detailsElement } = useFormAddress({
    form: form,
    formProps: {
      city: {
        // width: 'md',
        name: 'city',
      },
      // district: {
      //   width: 'md',
      // },
      // ward: {
      //   width: 'md',
      // },
      // address: {
      //   width: 'md',
      // },
    },
  });
  return (
    <ModalForm
      name="add:zone"
      form={form}
      width={500}
      onFinish={async (values) => {
        try {
          await createZone({
            label: values.label,
            image: values.image,
            project_id: values.project_id,
            city: values.city,
            district: values.district,
            ward: values.ward,
            address: values.address,

            lot: values.lot,
            lat: values.lat,
          });
          message.success(
            formatMessage({
              id: 'common.success',
            }),
          );
          onSuccess?.();
          return true;
        } catch (error: any) {
          // message.error(error?.message?.toString?.());
          return false;
        }
      }}
      open={open}
      onOpenChange={onOpenChange}
      trigger={trigger}
    >
      <Row>
        <Col span={24}>
          <FormUploadsPreviewable
            formItemName={'image'}
            label={formatMessage({ id: 'common.image_preview' })}
            fileLimit={1}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <ProFormText
            label={formatMessage({
              id: 'common.zone_name',
            })}
            name="label"
            rules={[{ required: true }]}
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            name="project_id"
            label={formatMessage({ id: 'common.project' })}
            showSearch
            request={async (params) => {
              let filters = [];
              if (params.keyWords) {
                filters.push([DOCTYPE_ERP.iotProject, 'label', 'like', `%${params.keyWords}%`]);
              }
              const res = await getProjectList({
                page: params.page,
                size: params.current,
                filters: filters,
              });
              return res.data.map((item) => ({
                label: item.label,
                value: item.name,
              }));
            }}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24}>
          <ProFormTextArea label={formatMessage({ id: 'common.description' })} name="description" />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>{cityElement}</Col>
        <Col span={12}>{districtElement}</Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>{wardElement}</Col>
        <Col span={12}>{detailsElement}</Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <ProForm.Item
            label={formatMessage({ id: 'diary.longitude' })}
            name="lot"
            rules={[{ required: true, message: 'Please enter longitude' }]}
          >
            <InputNumber style={{ width: '100%' }} />
          </ProForm.Item>
        </Col>
        <Col span={12}>
          <ProForm.Item
            label={formatMessage({ id: 'diary.latitude' })}
            name="lat"
            rules={[{ required: true, message: 'Please enter latitude' }]}
          >
            <InputNumber style={{ width: '100%' }} />
          </ProForm.Item>
        </Col>
        {/* <ProFormItem label={'Select Longitude, Latitude'} name="map">
        <ProFormMap />
        </ProFormItem> */}
      </Row>
    </ModalForm>
  );
};

const AddNewZone = withTriggerFormModal({
  defaultTrigger: ({ changeOpen }) => (
    <Button type="primary" icon={<PlusOutlined />} onClick={() => changeOpen(true)}>
      <FormattedMessage id="common.add_new_zone" />
    </Button>
  ),
  contentRender: ContentForm,
});
export default AddNewZone;
