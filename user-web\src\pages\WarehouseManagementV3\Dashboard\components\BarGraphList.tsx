import DetailBarGraphCard from '@/components/DetailBarGraphCard';
import {
  getTotalImportExportWarehouseQty,
  getTotalWarehouseQty,
  getTotalWarerhouseItemQty,
} from '@/services/stock/dashboard';
import { formatNumeralNoDecimal } from '@/services/utils';
import {
  CheckCircleFilled,
  CloseCircleFilled,
  DownloadOutlined,
  ExclamationCircleFilled,
  InfoCircleFilled,
} from '@ant-design/icons';
import { FormattedMessage, history, useIntl } from '@umijs/max';
import { Button, Card, Col, Row, Skeleton, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useDateRangeStore } from '../../hooks/useDateRangeStore';
import { useSelectedWarehousedStore } from '../../hooks/useWarehouseStore';

interface BarGraphData {
  label: string;
  totalAmount: string;
  backgroundColor: string;
  link: string;
}
const colors = ['#e0f7fa', '#fce4ec', '#fff3e0', '#e8f5e9', '#ede7f6', '#ffebee'];
const BarGraphList = () => {
  const { selectedWarehouse } = useSelectedWarehousedStore();
  const { dateRange } = useDateRangeStore();
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<BarGraphData[]>([]);
  const [stockStatus, setStockStatus] = useState();
  const intl = useIntl();
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      const fetchedData: BarGraphData[] = [];
      const filter = {
        end_date: dateRange.at(1)!,
        start_date: dateRange.at(0)!,
        warehouse: selectedWarehouse === 'all' ? undefined : selectedWarehouse,
      };
      const totalWarehouseQty = await getTotalWarehouseQty(filter);
      const stockStatus = await getTotalWarerhouseItemQty(filter);
      setStockStatus(stockStatus.stockStatusCounts);
      fetchedData.push({
        label: intl.formatMessage({ id: 'common.number_of_items' }),
        totalAmount: formatNumeralNoDecimal(totalWarehouseQty.result.unique_item_count),
        backgroundColor: colors[0],
        link: '/warehouse-management-v3/inventory/inventory-list',
      });
      fetchedData.push({
        label: intl.formatMessage({ id: 'common.stock_value' }),
        totalAmount: `${formatNumeralNoDecimal(totalWarehouseQty.result.total_price)} VNĐ`,
        backgroundColor: colors[1],
        link: '/warehouse-management-v3/inventory/inventory-list',
      });

      const totalImportExportWarehouseQty = await getTotalImportExportWarehouseQty(filter);
      fetchedData.push({
        label: intl.formatMessage({ id: 'common.import' }),
        totalAmount: `${formatNumeralNoDecimal(
          totalImportExportWarehouseQty.result.purchaseReceiptTotal.total_price,
        )} VNĐ`,
        backgroundColor: colors[2],
        link: '/warehouse-management-v3/inventory/import-history',
      });
      fetchedData.push({
        label: intl.formatMessage({ id: 'common.export' }),
        totalAmount: `${formatNumeralNoDecimal(
          totalImportExportWarehouseQty.result.deliveryNoteTotal.total_price,
        )} VNĐ`,
        backgroundColor: colors[3],
        link: '/warehouse-management-v3/inventory/export-history',
      });
      fetchedData.push({
        label: intl.formatMessage({ id: 'common.receive_directly' }),
        totalAmount: `${formatNumeralNoDecimal(
          totalImportExportWarehouseQty.result.materialReceiptTotal.total_price,
        )} VNĐ`,
        backgroundColor: colors[4],
        link: '/warehouse-management-v3/inventory/stock-entry',
      });
      fetchedData.push({
        label: intl.formatMessage({ id: 'common.export_cancellation' }),
        totalAmount: `${formatNumeralNoDecimal(
          Math.abs(totalImportExportWarehouseQty.result.materialIssueTotal.total_price),
        )} VNĐ`,
        backgroundColor: colors[5],
        link: '/warehouse-management-v3/inventory/stock-entry',
      });

      setData(fetchedData);
      setIsLoading(false);
    };

    fetchData();
  }, [selectedWarehouse, dateRange]);

  return (
    <Card
      style={{
        width: '100%',
      }}
    >
      <Skeleton loading={isLoading} />
      {data.length !== 0 && (
        <>
          <Row gutter={16}>
            {/* <Col lg={16} sm={24}>
          {data && (
            <List
              dataSource={data || []}
              grid={{ gutter: 16, column: 3 }}
              renderItem={(item, index) => {
                return (
                  <List.Item style={{ maxWidth: '100%', padding: '0px' }}>
                    <DetailBarGraphCard
                      label={item.label}
                      backgroundColor={item.backgroundColor}
                      link={item.link}
                      customInfo={<h4>{item.totalAmount}</h4>}
                    />
                  </List.Item>
                );
              }}
            ></List>
          )}
        </Col> */}
            <Col lg={6}>
              <Row gutter={16}>
                <Space
                  direction="vertical"
                  style={{
                    width: '100%',
                  }}
                >
                  <DetailBarGraphCard
                    label={data.at(0)!.label}
                    backgroundColor={data.at(0)!.backgroundColor}
                    link={data.at(0)!.link}
                    customInfo={data.at(0)!.totalAmount}
                  />
                  <DetailBarGraphCard
                    label={data.at(1)!.label}
                    backgroundColor={data.at(1)!.backgroundColor}
                    link={data.at(1)!.link}
                    customInfo={data.at(1)!.totalAmount}
                  />
                </Space>
              </Row>
            </Col>
            <Col lg={6}>
              <Space
                direction="vertical"
                style={{
                  width: '100%',
                }}
              >
                <DetailBarGraphCard
                  label={data.at(2)!.label}
                  backgroundColor={data.at(2)!.backgroundColor}
                  link={data.at(2)!.link}
                  customInfo={data.at(2)!.totalAmount}
                />
                <DetailBarGraphCard
                  label={data.at(3)!.label}
                  backgroundColor={data.at(3)!.backgroundColor}
                  link={data.at(3)!.link}
                  customInfo={data.at(3)!.totalAmount}
                />
              </Space>
            </Col>
            <Col lg={6}>
              <Space
                direction="vertical"
                style={{
                  width: '100%',
                }}
              >
                <DetailBarGraphCard
                  label={data.at(4)!.label}
                  backgroundColor={data.at(4)!.backgroundColor}
                  link={data.at(4)!.link}
                  customInfo={data.at(4)!.totalAmount}
                />
                <DetailBarGraphCard
                  label={data.at(5)!.label}
                  backgroundColor={data.at(5)!.backgroundColor}
                  link={data.at(5)!.link}
                  customInfo={data.at(5)!.totalAmount}
                />
              </Space>
            </Col>
            <Col lg={6}>
              <DetailBarGraphCard
                label={intl.formatMessage({ id: 'common.product_status' })}
                backgroundColor={'#EEF7FF'}
                link={'#'}
                customInfo={
                  <Space direction="vertical" size={14}>
                    <Row align="middle" style={{ fontSize: '14px' }}>
                      <CheckCircleFilled style={{ marginRight: '8px', color: '#52c41a' }} />
                      <span>
                        <FormattedMessage id={'common.in_stock'} />:{' '}
                        {stockStatus?.['common.in_stock']}
                      </span>
                    </Row>
                    <Row align="middle" style={{ fontSize: '14px' }}>
                      <CloseCircleFilled style={{ marginRight: '8px', color: '#FF0000' }} />
                      <span>
                        <FormattedMessage id={'common.out_stock'} />:{' '}
                        {stockStatus?.['common.out_stock']}
                      </span>
                    </Row>
                    <Row align="middle" style={{ fontSize: '14px' }}>
                      <ExclamationCircleFilled style={{ marginRight: '8px', color: '#faad14' }} />
                      <span>
                        <FormattedMessage id={'common.nearly_out_of_stock'} />:{' '}
                        {stockStatus?.['common.nearly_out_of_stock']}
                      </span>
                    </Row>
                    <Row align="middle" style={{ fontSize: '14px' }}>
                      <InfoCircleFilled style={{ marginRight: '8px', color: '#1890ff' }} />
                      <span>
                        <FormattedMessage id={'common.overstock'} />:{' '}
                        {stockStatus?.['common.overload']}
                      </span>
                    </Row>
                  </Space>
                }
              />
            </Col>
          </Row>
          <br />
          <Row>
            <Button
              onClick={() => {
                history.push('/warehouse-management-v3/inventory/report');
              }}
              type="primary"
            >
              <Space>
                <DownloadOutlined />
                <FormattedMessage id={'common.report'} />
              </Space>
            </Button>
          </Row>
        </>
      )}
    </Card>
  );
};

export default BarGraphList;
