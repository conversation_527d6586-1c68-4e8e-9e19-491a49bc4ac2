import Footer from '@/components/Footer';
import { forgotPassword } from '@/services/auth';
import { UserOutlined } from '@ant-design/icons';
import { LoginForm,ProFormText } from '@ant-design/pro-components';
import { Link } from '@umijs/max';
import { Col,message,Row } from 'antd';
import { createStyles } from 'antd-use-styles';
import React,{ useState } from 'react';
import styles from './index.less';

import forgotPassBg from '@/assets/img/forgot-pass-bg.png';
import logoTextWhite from '@/assets/img/logo-text-white.svg';
import ModalSendMailSuccess from './ModalSendMailSuccess';



const useStyles = createStyles(({ token }) => ({
  slideFooter: {
    color: 'white',
  },
  textLarge: {
    fontSize: token.fontSizeHeading1 + 10,
    fontWeight: 700,
  },
  textMedium: {
    fontSize: token.fontSizeHeading3,
    fontWeight: token.fontWeightStrong,
  },
  text: {
    fontSize: token.fontSizeHeading4,
    fontWeight: token.fontWeightStrong,
    lineHeight: 1.5,
  },
  textUppercase: {
    textTransform: 'uppercase',
  },
}));

const ForgotPassword: React.FC = () => {
  const style = useStyles();

  const [sendSucces, setSendSucess] = useState(false);
  const [email,setEmail] = useState<string|undefined>()

  const handleSubmit = async (values: { email: string }) => {
    try {
      // 登录
      const loginResp = await forgotPassword({ ...values });
      setEmail(values.email)
      console.log(loginResp);
      setSendSucess(true);
    } catch (error) {
      message.error('Email not found!');
    }
  };

  return (
    <>
      <ModalSendMailSuccess open={sendSucces} onOpenChange={setSendSucess} email={email} />
      <Row className={styles.container}>
        <Col md={12} xs={0} className={styles.side}>
          <div
            style={{
              paddingBlock: 45,
              paddingInline: 30,
            }}
          >
            <img src={logoTextWhite} />
          </div>
          <img className={styles.bg} src={forgotPassBg} alt="bg" />
          <div
            className={style.slideFooter}
            style={{
              paddingBlock: 45,
              paddingInline: 30,
            }}
          >
            <Row>
              <Col span={13}>
                <div className={style.textUppercase}>
                  <div className={style.textLarge}>Quản lý </div>
                  <div
                    className={style.textLarge}
                    style={{
                      marginBlockEnd: 10,
                    }}
                  >
                    thông minh
                  </div>
                  <div className={style.textMedium}>Trong nông nghiệp</div>
                </div>
              </Col>
              <Col span={9}>
                <div
                  className={style.text}
                  style={{
                    textAlign: 'right',
                  }}
                >
                  Với VIIS bạn có thể dễ dàng điều khiển, nhanh chóng, tiết kiệm thời gian và công
                  sức. Quản lý công việc dễ dàng và hiệu quả.
                </div>
              </Col>
            </Row>
          </div>
        </Col>
        <Col md={12} xs={24}>
          <div className={styles.content}>
            <LoginForm
              disabled={sendSucces}
              logo={<img style={{ width: '150px' }} alt="logo" src="/viis_logo.svg" />}
              title={<div style={{ marginTop: '20px' }}>Lấy lại mật khẩu</div>}
              subTitle={
                <>
                  Nhập đúng email bạn đã đăng ký, chúng tôi sẽ gửi mật khẩu xác nhận qua tài khoản
                  email của bạn
                </>
              }
              initialValues={{
                autoLogin: true,
              }}
              onFinish={async (values: { email: string }) => {
                await handleSubmit(values);
              }}
              submitter={{
                searchConfig: {
                  submitText: 'Gửi ngay',
                },
              }}
            >
              {/* {sendSucces ? (
              <Alert
                message="Gửi email thành công!"
                description="Nếu email của bạn có trong hệ thống của chúng tôi, bạn sẽ nhận được email sau vài phút. Nếu bạn không nhận được email nào, vui lỏng khởi động lại trang và thao tác lại."
                type="info"
                showIcon
                style={{ marginBottom: '20px' }}
              />
            ) : (
              <></>
            )} */}
              <ProFormText
                name="email"
                fieldProps={{
                  size: 'large',
                  prefix: <UserOutlined className={styles.prefixIcon} />,
                }}
                placeholder={'Email'}
                rules={[
                  {
                    required: true,
                    // message: 'field required',
                  },
                ]}
              />
            </LoginForm>
            <div
              style={{
                marginBottom: 24,
              }}
            >
              <div
                style={{
                  textAlign: 'center',
                }}
              >
                Bạn đã có tài khoản?
                <Link to={'/user/login'}> Đăng nhập</Link>
              </div>
            </div>
          </div>
          <Footer />
        </Col>
      </Row>
    </>
  );
};

export default ForgotPassword;
