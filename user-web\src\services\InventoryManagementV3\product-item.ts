import { IItemGroup } from '@/types/IItem';
import { request } from '@umijs/max';
import { generateAPIPath, getParamsReqList } from '../utils';

export type ProductItemV3 = {
  name: string;
  creation: string;
  modified: string;
  modified_by: string;
  owner: string;
  docstatus: number;
  idx: number;
  naming_series: string;
  item_code: string;
  item_name: string;
  item_group_label: string;
  item_group: string;
  stock_uom: string;
  uom_label: string;
  disabled: number;
  allow_alternative_item: number;
  is_stock_item: number;
  has_variants: number;
  include_item_in_manufacturing: number;
  opening_stock: number;
  valuation_rate: number;
  standard_rate: number;
  is_fixed_asset: number;
  auto_create_assets: number;
  is_grouped_asset: number;
  asset_category: any;
  asset_naming_series: any;
  over_delivery_receipt_allowance: number;
  over_billing_allowance: number;
  image: any;
  description: string;
  brand: any;
  shelf_life_in_days: number;
  end_of_life: string;
  default_material_request_type: string;
  valuation_method: string;
  warranty_period: any;
  weight_per_unit: number;
  weight_uom: any;
  allow_negative_stock: number;
  has_batch_no: number;
  create_new_batch: number;
  batch_number_series: any;
  has_expiry_date: number;
  retain_sample: number;
  sample_quantity: number;
  has_serial_no: number;
  serial_no_series: any;
  variant_of: any;
  variant_based_on: string;
  purchase_uom: any;
  min_order_qty: number;
  safety_stock: number;
  is_purchase_item: number;
  lead_time_days: number;
  last_purchase_rate: number;
  is_customer_provided_item: number;
  customer: any;
  delivered_by_supplier: number;
  enable_deferred_expense: number;
  deferred_expense_account: any;
  no_of_months_exp: number;
  country_of_origin: any;
  customs_tariff_number: any;
  sales_uom: any;
  grant_commission: number;
  is_sales_item: number;
  max_discount: number;
  enable_deferred_revenue: number;
  deferred_revenue_account: any;
  no_of_months: number;
  inspection_required_before_purchase: number;
  quality_inspection_template: any;
  inspection_required_before_delivery: number;
  is_sub_contracted_item: number;
  default_bom: any;
  customer_code: string;
  default_item_manufacturer: any;
  default_manufacturer_part_no: any;
  published_in_website: number;
  total_projected_qty: number;
  _user_tags: any;
  _comments: any;
  _assign: any;
  _liked_by: any;
  iot_customer: string;
  iot_customer_user: any;
  is_deleted: number;
  label?: string;
  uoms?: any[];
};

export async function getItemByGroup({
  page = 1,
  size = 10000,
  fields = ['*'],
  filters = [],
  or_filters = [],
}: API.ListParamsReq) {
  try {
    const params = {
      page,
      size,
      fields: JSON.stringify(fields),
      filters: JSON.stringify(filters),
    };
    const result = await request(generateAPIPath(`api/v2/item/itemGroupByGroup`), {
      method: 'GET',
      params: params,
      // params: params,
      // queryParams: params,
    });
    return {
      data: (result.result || []) as IItemGroup[],
    };
  } catch (error) {
    return { data: [] };
  }
}

export const getProductItemV3 = async (params?: API.ListParamsReq) => {
  const res = await request<API.PaginationResponseResult<ProductItemV3[]>>(
    generateAPIPath('api/v2/item'),
    {
      params: getParamsReqList(params),
    },
  );
  return res.result;
};

export interface CreateProductItemV3 {
  name?: string;
  image?: string;
  stock_uom: string;
  standard_rate: number; //gia ban mac dinh
  // "iot_customer": "09cb98f0-e0f5-11ec-b13b-4376e531a14a", //optional
  label: string;
  item_name: string;
  item_group: string;
  valuation_rate: number; //gia mua mac dinh
  description: string;
  max_stock_level: number;
  min_stock_level: number;
  stock_status?: string;
  stock_status_color?: string;
  uoms: UOM_V3[];
}

export interface UOM_V3 {
  doctype: string;
  uom: string;
  conversion_factor: number;
}

export const createProductItemV3 = async (data: CreateProductItemV3) => {
  const res = await request<API.ResponseResult<ProductItemV3>>(generateAPIPath('api/v2/item'), {
    method: 'POST',
    data,
  });
  return {
    data: res.result,
  };
};
export const updateProductItemV3 = async (data: CreateProductItemV3) => {
  const res = await request<API.ResponseResult<ProductItemV3>>(generateAPIPath('api/v2/item'), {
    method: 'PUT',
    data,
  });
  return {
    data: res.result,
  };
};
export const deleteProductItemV3 = async (data: { name: string; label: string }) => {
  const res = await request<API.ResponseResult<ProductItemV3>>(generateAPIPath('api/v2/item'), {
    method: 'PUT',
    data: {
      ...data,
      is_deleted: 1,
    },
  });
  return {
    data: res.result,
  };
};

export const getDetailsProductItemV3 = async (params: { name: string }) => {
  const res = await request<
    API.ResponseResult<{
      data: ProductItemV3[];
    }>
  >(generateAPIPath('api/v2/item/detail'), {
    params,
  });
  const data = res.result.data?.[0];
  if (!data) {
    throw new Error('Not found');
  }
  return {
    data,
  };
};
export interface InventoryVoucher {
  item_code: string;
  date: string;
  warehouse: string;
  posting_date: string;
  posting_time: string;
  actual_qty: number;
  incoming_rate: number;
  valuation_rate: number;
  company: string;
  iot_customer: string;
  iot_customer_user: string;
  customer: any;
  supplier: any;
  voucher_type: string;
  qty_after_transaction: number;
  stock_value_difference: number;
  voucher_no: string;
  stock_value: number;
  batch_no: string;
  serial_no: string;
  project: any;
  name: string;
  item_name: string;
  description: string;
  item_group: string;
  brand: any;
  stock_uom: string;
  in_qty: number;
  out_qty: number;
  in_out_rate: number;
  item_label: string;
  warehouse_label: string;
  uom_name: string;
  supplier_label: string;
  customer_label?: string;
  purpose?: string;
}
export const getItemInventoryVouchers = async (params: API.ListParamsReq & { name: string }) => {
  const res = await request<API.InventoryResponseResult<InventoryVoucher[]>>(
    generateAPIPath('api/v2/item/inventory-vouchers'),
    {
      params,
    },
  );
  return res.result;
};

export type CustomerData = {
  customer_id: number;
  customer_label: string;
  total_qty: number;
  total_price: number;
};
export const getItemCustomerVouchersSum = async (
  params: API.ListParamsReq & { name: string; customer_name?: string; customer_label?: string },
) => {
  const res = await request<API.PaginationResponseResult<CustomerData[]>>(
    generateAPIPath('api/v2/item/customer-vouchers'),
    {
      params,
    },
  );
  return res.result;
};

export type SupplierData = {
  supplier_id: number;
  supplier_label: string;
  total_qty: number;
  total_price: number;
};
export const getItemSupplierVouchersSum = async (
  params: API.ListParamsReq & { name: string; supplier_name?: string; supplier_label?: string },
) => {
  const res = await request<API.PaginationResponseResult<SupplierData[]>>(
    generateAPIPath('api/v2/item/supplier-vouchers'),
    {
      params,
    },
  );
  return res.result;
};
