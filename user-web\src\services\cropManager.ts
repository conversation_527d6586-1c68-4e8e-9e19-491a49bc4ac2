import { request } from '@umijs/max';
import { generateAPIPath, getParamsReqList } from './utils';

export type ICropRes = {
  name: string;
  creation: string;
  modified: string;
  modified_by: string;
  owner: string;
  docstatus: number;
  idx: number;
  farming_plan: any;
  label: string;
  description: string;
  zone: any;
  _user_tags: any;
  _comments: any;
  _assign: any;
  _liked_by: any;
  crops: any;
  plant_id: string;
  plant: any;
  zone_id: string;
  square: string;
  start_date: string;
  end_date: string;
  image: string;
  avatar: string;
  estimate_quantity: any;
  quantity_estimate: number;
  status: string;
  plant_label: string;
};
export const getCropList = async (params?: API.ListParamsReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ICropRes[];
    }>
  >(generateAPIPath('api/v2/cropManage/crop'), {
    method: 'GET',
    params: getParamsReqList(params),
  });
  return res.result;
};

export const getTemplateCropList = async (params?: API.ListParamsReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ICropRes[];
    }>
  >(generateAPIPath('api/v2/cropManage/crop/template'), {
    method: 'GET',
    params: getParamsReqList(params),
  });
  return res.result;
};

export type ICropManagerInfo = {
  avatar: string | null;
  name: string;
  label: string;
  plant_id: string;
  plant_name: string;
  zone_id: string;
  zone_name: string;
  project_id: string;
  project_name: string;
  start_date: string | null;
  end_date: string | null;
  status: string | null;
  dateFilter: 'this_week' | 'this_month' | 'this_year' | 'next_3_months' | null;
  is_template: boolean;
};
export const getCropManagementInfoList = async (params?: API.ListParamsReq) => {
  const res = await request<API.PaginationResponseResult<ICropManagerInfo[]>>(
    generateAPIPath('api/v2/cropManage/crop-management-info'),
    {
      method: 'GET',
      params: params,
    },
  );
  return res.result;
};
export const getCurrentStateOfCrop = async (cropId: string) => {
  const res = await request<
    API.ResponseResult<
      {
        name: string;
        label: string;
        start_date: string;
        end_date: string;
      }[]
    >
  >(generateAPIPath('api/v2/cropManage/crop-current-state'), {
    method: 'GET',
    params: {
      crop_id: cropId,
    },
  });
  return res.result;
};
export type ICreateCropReq = {
  label: string;
  zone_id: string;
  start_date: string;
  end_date: string;
  square: number;
  plant_id: string;
  image?: string;
  description?: string;
  status?: string;
  is_template?: boolean;
  quantity_estimate?: number;
};
export interface ICreateCropRes {
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  docstatus: number;
  idx: number;
  plant_id: any;
  plant_label: any;
  zone_id: string;
  zone: any;
  image: any;
  label: string;
  description: any;
  square: any;
  start_date: any;
  end_date: any;
  quantity_estimate: number;
  status: string;
  doctype: string;
}

export const createCrop = async (data: ICreateCropReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ICreateCropRes;
    }>
  >(generateAPIPath('api/v2/cropManage/crop'), {
    method: 'POST',
    data,
  });
  return res.result;
};
export type IUpdateCropReq = {
  name: string;
  label?: string;
  zone_id?: string;
  start_date?: string;
  end_date?: string;
  square?: number;
  plant_id?: string;
  image?: string | null;
  avatar?: string | null;
  description?: string;
  status?: string;
  quantity_estimate?: number;
  is_deleted?: number;
};
export const updateCrop = async (data: IUpdateCropReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ICreateCropRes;
    }>
  >(generateAPIPath('api/v2/cropManage/crop'), {
    method: 'PUT',
    data,
  });
  return res.result;
};
export interface ICropNoteRes {
  name: string;
  creation: string;
  modified: string;
  modified_by: string;
  owner: string;
  docstatus: number;
  idx: number;
  crop: string;
  label: string;
  note: string;
  _user_tags: any;
  _comments: any;
  _assign: any;
  _liked_by: any;
  image: any;
  crop_name: string;
  first_name: string;
  last_name: string;
}

export const getCropNoteList = async (params?: API.ListParamsReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ICropNoteRes[];
    }>
  >(generateAPIPath('api/v2/cropManage/note'), {
    method: 'GET',
    params: getParamsReqList(params),
  });
  return res.result;
};

export type ICreateCropNoteReq = {
  crop: string;
  label: string;
  note?: string;
  image?: string;
};
export interface ICreateCropNoteRes {
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  docstatus: number;
  idx: number;
  iot_crop: string;
  crop_name: string;
  label: string;
  description: string;
  image: string;
  doctype: string;
}

export const createCropNote = async (data: ICreateCropNoteReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ICreateCropNoteRes;
    }>
  >(generateAPIPath('api/v2/cropManage/note'), {
    method: 'POST',
    data,
  });
  return res.result;
};

export interface IUpdateCropNoteReq {
  name: string;
  label?: string;
  note?: string;
  image?: string;
  crop: string;
}

export const updateCropNote = async (data: IUpdateCropNoteReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ICropNoteRes;
    }>
  >(generateAPIPath('api/v2/cropManage/note'), {
    method: 'PUT',
    data,
  });
  return res.result;
};

export const deleteCropNote = async ({ name }: { name: string }) => {
  const res = await request<
    API.ResponseResult<{
      exc_type: 'DoesNotExistError';
      message: 'ok';
    }>
  >(generateAPIPath(`api/v2/cropManage/note?name=${name}`), {
    method: 'DELETE',
  });
  return res.result;
};
// Participants
export const getParticipantsInCrop = async (params?: API.ListParamsReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ICropNoteRes[];
    }>
  >(generateAPIPath('api/v2/cropManage/iot_employee_in_crop'), {
    method: 'GET',
    params: getParamsReqList(params),
  });
  return res.result;
};

export const addParticipantInCrop = async (data: {
  iot_crop: string;
  iot_customer_user: string;
}) => {
  const res = await request<
    API.ResponseResult<{
      data: ICropNoteRes[];
    }>
  >(generateAPIPath('api/v2/cropManage/iot_employee_in_crop'), {
    method: 'POST',
    data,
  });
  return res;
};

export const updateParticipantsInCrop = async (data: {
  name: string;
  iot_crop: string;
  iot_customer_user: string;
}) => {
  const res = await request<
    API.ResponseResult<{
      data: ICropNoteRes[];
    }>
  >(generateAPIPath('api/v2/cropManage/iot_employee_in_crop'), {
    method: 'PUT',
    data,
  });
  return res;
};

export const deleteParticipantsInCrop = async (name: string) => {
  const res = await request(generateAPIPath('api/v2/cropManage/iot_employee_in_crop'), {
    method: 'DELETE',
    params: {
      name,
    },
  });
  return res;
};

export interface IStatisticPestRes {
  pest_id: string;
  pest_label: string;
  category_id?: string;
  category_label?: string;
  state_id?: string;
  state_label?: string;
  customer_user_id?: string;
  first_name?: string;
  last_name?: string;
  image: string;
}
export const getStatisticPestList = async (params?: API.ListParamsReq & { cropId?: string }) => {
  const res = await request<API.PaginationResponseResult<IStatisticPestRes[]>>(
    generateAPIPath(`api/v2/cropManage/statisticPestList?crop_id=${params?.cropId}`),
    {
      method: 'GET',
      params: getParamsReqList(params),
    },
  );
  return res.result;
};

export interface IStatisticNoteRes {
  note_id: string;
  note_label?: string;
  image?: string;
  note: string;
  creation: string;
  modified: string;
}
export const getStatisticNoteList = async (params?: API.ListParamsReq & { cropId?: string }) => {
  const res = await request<API.PaginationResponseResult<IStatisticNoteRes[]>>(
    generateAPIPath(`api/v2/cropManage/statisticNoteList?crop_id=${params?.cropId}`),
    {
      method: 'GET',
      params: getParamsReqList(params),
    },
  );
  return res.result;
};
