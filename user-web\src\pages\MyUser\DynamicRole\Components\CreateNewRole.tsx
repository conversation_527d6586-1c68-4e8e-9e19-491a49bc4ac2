import {
  createDynamicRole,
  IIotDynamicRole,
  listDynamicRoleAllSection,
} from '@/services/customerUser';
import { PlusOutlined } from '@ant-design/icons';
import { useIntl } from '@umijs/max';
import { Button, Checkbox, Col, Form, Input, message, Modal, Row, Typography } from 'antd';
import { useEffect, useState } from 'react';

const { Item } = Form;
const { Title } = Typography;

const CreateRoleForm = (params: { refreshFnc: any; customer_id: any }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [sectionCategories, setSectionCategories] = useState<any>([]);
  const [showFieldLevelPermissions, setShowFieldLevelPermissions] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const data: any = await listDynamicRoleAllSection();
      const sectionArray = Object.entries(data).map(([key, value]) => ({
        label: key,
        value: value,
      }));
      categorizeActions(sectionArray);
    } catch (error) {
      console.error('Error fetching sections data:', error);
    }
  };

  const categorizeActions = (data: any) => {
    const categories: any = {
      System: [],
      Project: [],
      Zone: [],
      Plant: [],
      Crop: [],
      Task: [],
      Plan: [],
      State: [],
      Category: [],
      Storage: [],
      CategoryInventory: [],
      CategoryInventoryFieldLevel: [],
      Employee: [],
      DynamicRole: [],
      Timekeeping: [],
      Visitor: [],
      IoTDevice: [],
    };

    Object.entries(data).forEach(([index, obj]) => {
      const { label, value } = obj as any;
      if (label.includes('PROJECT')) {
        categories.Project.push({ label: value, value: label });
      } else if (label.includes('ZONE')) {
        categories.Zone.push({ label: value, value: label });
      } else if (label.includes('PLANT')) {
        categories.Plant.push({ label: value, value: label });
      } else if (label.includes('CROP')) {
        categories.Crop.push({ label: value, value: label });
      } else if (label.includes('TASK')) {
        categories.Task.push({ label: value, value: label });
      } else if (label.includes('STATE')) {
        categories.State.push({ label: value, value: label });
      } else if (label.includes('CATEGORY') && !label.includes('CATEGORY_INVENTORY')) {
        categories.Category.push({ label: value, value: label });
      } else if (label.includes('STORAGE')) {
        categories.Storage.push({ label: value, value: label });
      } else if (
        label.includes('CATEGORY_INVENTORY') &&
        !label.includes('CATEGORY_INVENTORY_FIELD_LEVEL')
      ) {
        categories.CategoryInventory.push({ label: value, value: label });
      } else if (label.includes('CATEGORY_INVENTORY_FIELD_LEVEL')) {
        categories.CategoryInventoryFieldLevel.push({ label: value, value: label });
      } else if (label.includes('EMPLOYEE')) {
        categories.Employee.push({ label: value, value: label });
      } else if (label.includes('DYNAMIC_ROLE')) {
        categories.DynamicRole.push({ label: value, value: label });
      } else if (label.includes('TIMEKEEPING')) {
        categories.Timekeeping.push({ label: value, value: label });
      } else if (label.includes('VISITOR')) {
        categories.Visitor.push({ label: value, value: label });
      } else if (label.includes('SYSTEM')) {
        categories.System.push({ label: value, value: label });
      } else if (label.includes('IOT_DEVICE')) {
        categories.IoTDevice.push({ label: value, value: label });
      }
    });
    setSectionCategories(categories);
  };

  const showModal = () => {
    setOpen(true);
  };

  const hideModal = () => {
    setOpen(false);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  const handleReset = () => {
    form.resetFields();
  };

  const { formatMessage } = useIntl();

  const handleFieldLevelPermissionsChange = (e: any) => {
    setShowFieldLevelPermissions(e.target.checked);
  };

  return (
    <>
      <Button type="primary" onClick={showModal} style={{ display: 'flex', alignItems: 'center' }}>
        <PlusOutlined /> {formatMessage({ id: 'common.add_new_role' })}
      </Button>
      <Modal
        title={formatMessage({ id: 'common.add_new_role' })}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              const values = Object.entries(value)
                .filter(([key, value]) => key !== 'label' && Array.isArray(value))
                .map(([key, value]) => value)
                .flat();
              const flattenedString = values.join(',');
              const dynamicRole: IIotDynamicRole = {
                label: value.label,
                iot_customer: params.customer_id,
                sections: flattenedString,
              };
              const req = await createDynamicRole(dynamicRole);
              hideModal();
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
              message.success('Success!');
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={16}>
            <Col md={12}>
              <Item
                label={formatMessage({ id: 'common.role_name' })}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="label"
              >
                <Input />
              </Item>
            </Col>
            <Col span={24} style={{ textAlign: 'left', marginBottom: '16px' }}>
              <Button size="small" type="default" onClick={handleReset}>
                Tạo lại
              </Button>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col md={6}>
              <Title level={5}>Quản trị viên</Title>
              <Item name="system_section">
                <Checkbox.Group options={sectionCategories.System} />
              </Item>
            </Col>
            <Col md={6}>
              <Title level={5}>Quyền dự án</Title>
              <Item name="project_section">
                <Checkbox.Group options={sectionCategories.Project} />
              </Item>
            </Col>
            <Col md={6}>
              <Title level={5}>Quyền khu vực</Title>
              <Item name="zone_section">
                <Checkbox.Group options={sectionCategories.Zone} />
              </Item>
            </Col>
            <Col md={6}>
              <Title level={5}>Quyền cây trồng</Title>
              <Item name="plant_section">
                <Checkbox.Group options={sectionCategories.Plant} />
              </Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col md={6}>
              <Title level={5}>Quyền mùa vụ</Title>
              <Item name="crop_section">
                <Checkbox.Group options={sectionCategories.Crop} />
              </Item>
            </Col>
            <Col md={6}>
              <Title level={5}>Quyền giai đoạn</Title>
              <Item name="state_section">
                <Checkbox.Group options={sectionCategories.State} />
              </Item>
            </Col>
            <Col md={6}>
              <Title level={5}>Quyền công việc</Title>
              <Item name="task_section">
                <Checkbox.Group options={sectionCategories.Task} />
              </Item>
            </Col>
            <Col md={6}>
              <Title level={5}>Quyền hàng hóa</Title>
              <Item name="category_section">
                <Checkbox.Group options={sectionCategories.Category} />
              </Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col md={6}>
              <Title level={5}>Quyền kho</Title>
              <Item name="storage_section">
                <Checkbox.Group options={sectionCategories.Storage} />
              </Item>
            </Col>

            <Col md={6}>
              <Title level={5}>Quyền tồn kho</Title>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0px' }}>
                <Item name="category_inventory_section" style={{ marginBottom: '0px' }}>
                  <Checkbox.Group options={sectionCategories.CategoryInventory} />
                </Item>
                <Checkbox onChange={handleFieldLevelPermissionsChange} style={{ marginTop: '0px' }}>
                  Quyền chi tiết
                </Checkbox>
                {showFieldLevelPermissions && (
                  <div style={{ paddingLeft: '24px', marginTop: '8px' }}>
                    <Item name="category_inventory_field_level_section">
                      <Checkbox.Group options={sectionCategories.CategoryInventoryFieldLevel} />
                    </Item>
                  </div>
                )}
              </div>
            </Col>

            <Col md={6}>
              <Title level={5}>Quản lý nhân viên</Title>
              <Item name="employee_section">
                <Checkbox.Group options={sectionCategories.Employee} />
              </Item>
            </Col>
            <Col md={6}>
              <Title level={5}>Quản lý vai trò</Title>
              <Item name="dynamic_role_section">
                <Checkbox.Group options={sectionCategories.DynamicRole} />
              </Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col md={6}>
              <Title level={5}>Quản lý chấm công</Title>
              <Item name="dynamic_timekeeping_section">
                <Checkbox.Group options={sectionCategories.Timekeeping} />
              </Item>
            </Col>
            <Col md={6}>
              <Title level={5}>Khách tham quan</Title>
              <Item name="visitor_section">
                <Checkbox.Group options={sectionCategories.Visitor} />
              </Item>
            </Col>
            <Col md={6}>
              <Title level={5}>Quản lý thiết bị IoT</Title>
              <Item name="iot_device_section">
                <Checkbox.Group options={sectionCategories.IoTDevice} />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default CreateRoleForm;
