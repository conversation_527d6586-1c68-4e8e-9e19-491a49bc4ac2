import FallbackComponent from '@/components/FallbackContent';
import { updateCustomerUser } from '@/services/customerUser';
import { sscriptGeneralList } from '@/services/sscript';
import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Access, useAccess, useIntl, useModel, useSearchParams } from '@umijs/max';
import { ConfigProvider, Switch } from 'antd';
import React, { useRef } from 'react';
import CreateRoleForm from '../DynamicRole/Components/CreateNewRole';
import CreateCustomerUserForm from './Components/CreateCustomerUserForm';
import UpdateCustomerUserForm from './Components/UpdateCustomerUserForm';

interface ActionType {
  reload: (resetPageIndex?: boolean) => void;
  reloadAndRest: () => void;
  reset: () => void;
  clearSelected?: () => void;
  startEditable: (rowKey: string) => boolean;
  cancelEditable: (rowKey: string) => boolean;
}

const CustomerUser: React.FC = () => {
  const { formatMessage } = useIntl();
  const tableRef = useRef<ActionType>();
  const [searchParams, setSearchParams] = useSearchParams();
  const { initialState } = useModel('@@initialState');
  const customer_name = initialState?.currentUser?.customer_id;
  const isAdmin = initialState?.currentUser?.sections.includes('SYSTEM_ADMIN');
  console.log('isAdmin', initialState?.currentUser);
  if (!customer_name) return <></>;
  const access = useAccess();
  const canRead = access.canAccessPageEmployeeNewManagement();
  const canUpdate = access.canUpdateInEmployeeNewManagement();
  const canCreateUser = access.canCreateInEmployeeNewManagement();
  const canCreateRole = access.canCreateInRoleManagement();

  const columns: ProColumns<API.User>[] = [
    {
      title: formatMessage({
        id: 'common.action',
      }),
      dataIndex: 'name',
      render: (dom: any, entity: any) => {
        if (canUpdate) {
          return <UpdateCustomerUserForm refreshFnc={reloadTable} customerUser={entity} />;
        }
      },
      fixed: 'left',
      hideInTable: !canUpdate,
      align: 'center',
      width: 40,
    },
    {
      title: 'Email',
      dataIndex: 'email',
      align: 'center',
      width: 80,
    },
    {
      title: formatMessage({
        id: 'common.role',
      }),
      dataIndex: 'iot_dynamic_role',
      render: (dom: any, entity: any) => {
        return <>{entity.role_label}</>;
      },
      align: 'center',
      width: 60,
    },
    {
      title: formatMessage({
        id: 'common.last_name',
      }),
      dataIndex: 'last_name',
      align: 'center',
      width: 40,
    },
    {
      title: formatMessage({
        id: 'common.first_name',
      }),
      dataIndex: 'first_name',
      align: 'center',
      width: 40,
    },
    {
      title: 'Phone',
      dataIndex: 'phone_number',
      align: 'center',
      width: 60,
    },
    {
      title: formatMessage({
        id: 'common.active',
      }),
      dataIndex: 'is_deactivated',
      align: 'center',
      width: 40,
      render: (dom, entity, index, action, schema) => {
        return (
          <Switch
            disabled={isAdmin ? false : true}
            checked={!entity.is_deactivated}
            onChange={async (checked) => {
              const newIsDeactivated = checked ? 0 : 1;
              await updateCustomerUser({
                name: entity.name,
                is_deactivated: newIsDeactivated,
              });
              tableRef.current?.reload();
            }}
          />
        );
      },
    },
  ];

  const reloadTable = async () => {
    tableRef.current?.reload();
  };

  const renderButtons: any = [];
  if (canCreateUser) {
    renderButtons.push(
      <CreateCustomerUserForm
        refreshFnc={reloadTable}
        customer_id={customer_name}
        key="create_user"
      />,
    );
  }
  if (canCreateRole) {
    renderButtons.push(
      <CreateRoleForm refreshFnc={reloadTable} customer_id={customer_name} key="create_role" />,
    );
  }
  return (
    <Access accessible={canRead} fallback={<FallbackComponent />}>
      <PageContainer>
        <ConfigProvider>
          <ProTable<API.User, API.PageParams>
            scroll={{ x: 1200, y: 600 }}
            size="small"
            actionRef={tableRef}
            rowKey="name"
            // loading={loading}
            // dataSource={[...users]}
            request={async (params, sort, filter) => {
              let order_by = 'email, modified desc';
              if (Object.keys(sort).length) {
                order_by = `${Object.keys(sort)[0]} ${
                  Object.values(sort)[0] === 'ascend' ? 'asc' : 'desc'
                }`;
              }
              const { current, pageSize } = params;
              type fieldKeyType = keyof typeof params;
              const searchFields = Object.keys(params).filter((field: string) => {
                const value = params[field as fieldKeyType];
                return field !== 'current' && field !== 'pageSize' && value !== 'all';
              });
              const filterArr = searchFields.map((field) => {
                const value = params[field as fieldKeyType];
                return ['iot_customer_user', field, 'like', `%${value}%`];
              });

              if (customer_name) {
                filterArr.push(['iot_customer_user', 'customer_id', 'like', customer_name]);
              }

              try {
                const result = await sscriptGeneralList({
                  doc_name: 'iot_customer_user',
                  filters: filterArr,
                  page: current ? current : 0 + 1,
                  size: pageSize,
                  fields: ['*'],
                  order_by: order_by,
                });
                return {
                  data: result.data,
                  success: true,
                  total: result.pagination.totalElements,
                };
              } catch (error) {
                console.log(error);
              } finally {
              }
            }}
            bordered
            columns={columns}
            search={false}
            toolBarRender={() => renderButtons}
            pagination={{
              defaultPageSize: 20,
              showSizeChanger: true,
              pageSizeOptions: ['20', '50', '100'],
            }}
          />
        </ConfigProvider>
      </PageContainer>
    </Access>
  );
};

export default CustomerUser;
