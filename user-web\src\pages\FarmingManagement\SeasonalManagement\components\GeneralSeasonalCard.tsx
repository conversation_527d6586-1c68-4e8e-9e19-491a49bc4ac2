import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import { dayjsUtil } from '@/utils/date';
import { genDownloadUrl } from '@/utils/file';
import { EditOutlined } from '@ant-design/icons';
import { history, useIntl } from '@umijs/max';
import { Avatar, Card, Flex, Tag } from 'antd';
import Meta from 'antd/es/card/Meta';
import { useMemo } from 'react';

interface Props {
  label?: string;
  avatar?: string;
  name: string;
  start_date?: string;
  end_date?: string;
  status?: string;
}

const formatDate = (dateString: any) => {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

const GeneralSeasonalCard = ({ label, avatar, name, start_date, end_date, ...rest }: Props) => {
  function handleDelete(): void {
    throw new Error('Function not implemented.');
  }
  const totalDate = useMemo(
    () => dayjsUtil(end_date).diff(start_date, 'd'),
    [start_date, end_date],
  );
  const daysLeft = useMemo(() => dayjsUtil(end_date).diff(dayjsUtil(), 'd'), [end_date]);
  const isDone = useMemo(() => rest.status === 'Done', [rest.status]);

  const { formatMessage } = useIntl();
  return (
    <Card
      size="default"
      onClick={() => {
        history.push(`/farming-management/seasonal-management/detail/${name}`);
      }}
      hoverable
      actions={[
        <EditOutlined key="edit" />,
        // <Popconfirm
        //   title="Xoá vụ mùa"
        //   description={`Bạn có muốn xoá vụ mùa ${label}?`}
        //   onConfirm={() => handleDelete()}
        //   key="delete"
        //   onPopupClick={(e) => {
        //     e.stopPropagation();
        //   }}
        // >
        //   <DeleteOutlined
        //     key="delete"
        //     onClick={(e) => {
        //       e.stopPropagation();
        //     }}
        //   />
        // </Popconfirm>,
      ]}
    >
      <Meta
        avatar={
          <Avatar
            shape="square"
            size={54}
            src={avatar ? genDownloadUrl(avatar) : DEFAULT_FALLBACK_IMG}
          />
        }
        title={label}
        description={
          <Flex vertical gap="small">
            <div>{`${formatDate(start_date)} - ${formatDate(end_date)}`}</div>
            <Tag color="processing">
              {formatMessage({
                id: 'common.number_of_days_of_implementation',
              })}
              : {totalDate} {formatMessage({ id: 'common.date' })}
            </Tag>
            <Tag color={isDone ? 'success' : daysLeft >= 0 ? 'processing' : 'error'}>
              {isDone
                ? formatMessage({
                    id: 'common.the-harvest-season-is-finished',
                  })
                : daysLeft >= 0
                ? `${formatMessage({
                    id: 'common.the-harvest-season-ends-in',
                  })}: ${daysLeft} ${formatMessage({ id: 'common.date' })}`
                : `${formatMessage({
                    id: 'common.the-harvest-season-is-overdue',
                  })}: ${-daysLeft} ${formatMessage({ id: 'common.date' })}`}
            </Tag>
          </Flex>
        }
      ></Meta>
    </Card>
  );
};

export default GeneralSeasonalCard;
