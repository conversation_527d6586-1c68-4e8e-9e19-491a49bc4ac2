import { ActionType, ProTable, useDeepCompareEffect } from '@ant-design/pro-components';
import { FormattedMessage, Link, useAccess, useIntl } from '@umijs/max';

import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getCropManagementInfoList, ICropManagerInfo } from '@/services/cropManager';
import { getParamsReqTable } from '@/services/utils';
import { formatDateDefault } from '@/utils/date';
import { genDownloadUrl } from '@/utils/file';
import { useDebounceFn } from 'ahooks';
import { Avatar, Card, Flex, Spin } from 'antd';
import { FC, startTransition, useRef, useState } from 'react';
import { useGetCropManagementInfoList } from '../hooks/useGetCropManagementInfoList';
import Filter from './Filter';
type SearchParams = {
  crop_name?: string;
  startDate?: string;
  endDate?: string;
  zone_id?: string;
  plant_id?: string;
};
interface CropPlanProps {
  // data?: ICropManagerInfo[];
  query?: SearchParams;
  status?: Array<'In progress' | 'Done' | 'Working'>;
}

const SeasonalManagementTable: FC<CropPlanProps> = ({ query, status }) => {
  const actionRef = useRef<ActionType>(null);
  useDeepCompareEffect(() => {
    actionRef.current?.reload();
  }, [query]);
  return (
    <ProTable<ICropManagerInfo>
      actionRef={actionRef}
      pagination={{
        defaultPageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['20', '50', '100'],
      }}
      search={false}
      rowKey={'name'}
      columns={[
        // {
        //   title: 'ID',
        //   copyable: true,
        //   dataIndex: 'name',
        //   width: 200,
        // },
        {
          title: <FormattedMessage id="storage-management.category-management.object_name" />,
          dataIndex: 'label',
          sorter: true,
          render(dom, entity, index, action, schema) {
            return (
              <Link to={`/farming-management/seasonal-management/detail/${entity.name}`}>
                {dom}
              </Link>
            );
          },
        },
        {
          title: <FormattedMessage id="common.form.image" defaultMessage="Image" />,
          render(dom, entity, index, action, schema) {
            return <Avatar src={entity?.avatar ? genDownloadUrl(entity.avatar) : undefined} />;
          },
        },
        {
          sorter: true,
          title: <FormattedMessage id="common.zone_name" />,
          dataIndex: 'zone_name',
        },
        {
          sorter: true,

          title: <FormattedMessage id="common.project-name" />,
          dataIndex: 'project_name',
        },
        // {
        //   title: <FormattedMessage id="common.plan-name" />,
        //   dataIndex: 'plan_name',
        // },
        {
          sorter: true,
          title: <FormattedMessage id="common.start_date" />,
          dataIndex: 'start_date',
          valueType: 'dateTime',
          render: (text: any, record, index, action) => formatDateDefault(record.start_date),
        },
        {
          sorter: true,
          title: <FormattedMessage id="common.end_date" />,
          dataIndex: 'end_date',
          valueType: 'dateTime',
          render: (text: any, record, index, action) => formatDateDefault(record.end_date),
        },
        // {
        //   title: 'Ngày tạo',
        //   dataIndex: 'creation',
        //   valueType: 'fromNow',
        // },
        // {
        //   title: 'Ngày chỉnh sửa',
        //   dataIndex: 'modified',
        //   valueType: 'fromNow',
        // },
      ]}
      options={false}
      // dataSource={data as any}
      request={async (params, sort, filter) => {
        const paramsReq = getParamsReqTable({
          defaultSort: 'crop.creation desc',
          doc_name: DOCTYPE_ERP.iotCrop,
          tableReqParams: {
            params,
            sort,
            filter,
          },
        });
        const res = await getCropManagementInfoList({
          ...paramsReq,
          ...query,
          status: status?.length ? JSON.stringify(status) : undefined,
        } as any);
        return {
          data: res.data,
          success: true,
          total: res?.pagination?.totalElements,
        };
      }}
      size="small"
    />
  );
};

const SeasonalTableView: FC = () => {
  const intl = useIntl();

  const access = useAccess();
  const [search, setSearchParams] = useState<SearchParams>({});

  const canCreate = access.canCreateInSeasonalManagement();
  const { cropInProgress, loading, cropFinished, allCrop } = useGetCropManagementInfoList(search);
  const { run: handleSearch } = useDebounceFn(
    (newSearchParams: Partial<SearchParams>) => {
      startTransition(() => {
        setSearchParams({
          ...search,
          ...newSearchParams,
        });
      });
    },
    {
      wait: 800,
    },
  );

  return (
    <Spin spinning={loading}>
      <Flex vertical gap="middle">
        <Filter
          canCreateCrop={canCreate}
          handleSearch={(e) => {
            handleSearch({
              crop_name: e.target.value,
            });
          }}
          handleSearchDate={(e: any) => {
            const startDate = e?.[0] ? e[0].format('YYYY-MM-DD HH:mm:ss') : undefined;
            const endDate = e?.[1] ? e[1].format('YYYY-MM-DD HH:mm:ss') : undefined;
            handleSearch({
              startDate,
              endDate,
            });
          }}
          onPlantChange={(val) => {
            handleSearch({
              plant_id: val,
            });
          }}
          onZoneChange={(e) => {
            handleSearch({
              zone_id: e,
            });
          }}
        />

        <Card
          tabList={[
            {
              label: intl.formatMessage({ id: 'seasonalTab.ongoing' }),
              children: (
                <SeasonalManagementTable query={search} status={['In progress', 'Working']} />
              ),
              key: 'cropInProgress',
            },
            {
              label: intl.formatMessage({ id: 'seasonalTab.completed' }),
              children: <SeasonalManagementTable query={search} status={['Done']} />,
              key: 'cropFinished',
            },
            {
              label: intl.formatMessage({ id: 'seasonalTab.all' }),
              children: <SeasonalManagementTable query={search} />,
              key: 'allCrop',
            },
          ]}
        />
      </Flex>
    </Spin>
  );
};
export default SeasonalTableView;
