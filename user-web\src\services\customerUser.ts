import { request } from '@umijs/max';
import { generalDelete } from './sscript';
import { generateAPIPath, getParamsReqList } from './utils';

export type ICustomerUserRes = {
  name: string;
  creation: string;
  modified: string;
  modified_by: string;
  owner: string;
  docstatus: number;
  idx: number;
  user_id: string;
  _user_tags: any;
  _comments: any;
  _assign: any;
  _liked_by: any;
  id: any;
  user_name: string;
  created_time: any;
  iot_customer_id: any;
  user_avatar: any;
  email: string;
  full_name: string;
  phone_number: any;
  address: any;
  date_join: any;
  date_active: any;
  date_warranty: any;
  customer_id: string;
  first_name: any;
  lats_name: any;
  last_name: any;
  district: any;
  ward: any;
  province: any;
};
export interface CreateUserDataReq {
  email: string;
  first_name: string;
  last_name: string;
  password: string;
  is_admin: number;
  phone_number: string;
  province: string;
  district: string;
  ward: string;
  address: string;
  description?: string;
}

export class IIotDynamicRole {
  name?: string;
  label?: string; // Data
  role?: string; // Data
  iot_customer?: string; // Link
  sections?: string; // Data
}

export const createCustomerUser = async (data: CreateUserDataReq) => {
  const res = await request(generateAPIPath('api/v2/register/customer-user-with-role'), {
    method: 'POST',
    data,
  });
  return res;
};
export const getCustomerUserList = async (params?: API.ListParamsReq) => {
  const res = await request<
    API.ResponseResult<{
      data: ICustomerUserRes[];
    }>
  >(generateAPIPath('api/v2/customerUser/user'), {
    params: getParamsReqList(params),
  });
  return res.result;
};

export const getCustomerUserIndividualList = async (params?: API.ListParamsReq) => {
  const res = await request<{ result: any[] }>(
    generateAPIPath('api/v2/customerUser/user/individual'),
    {
      params: getParamsReqList(params),
    },
  );
  return res.result;
};

export async function customerUserListAll() {
  try {
    const result = await request(generateAPIPath(`api/v2/customerUser/user`), {
      method: 'GET',
      params: { fields: ['*'] },
    });
    return result.result;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

//update customer user
export const updateCustomerUser = async (data: any) => {
  const res = await request(generateAPIPath('api/v2/customerUser/user'), {
    method: 'PUT',
    data,
  });
  return res;
};

//delete customer user
export const deleteCustomerUser = async (name: string) => {
  const res = await request(generateAPIPath('api/v2/customerUser/user'), {
    method: 'DELETE',
    params: { name },
  });
  return res;
};

//delete customer user credential
export const deleteCustomerUserCredential = async (name: string) => {
  const res = await request(generateAPIPath('api/v2/customerUser/user-credential'), {
    method: 'DELETE',
    params: { name },
  });
  return res;
};
/**
 *
 * DYNAMIC ROLE APIs
 */
export async function listDynamicRoleAllSection() {
  try {
    const result = await request(generateAPIPath(`api/v2/dynamicRole/listAllSection`), {
      method: 'GET',
    });
    return result.result;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getDynamicRole() {
  try {
    const result = await request(generateAPIPath(`api/v2/dynamicRole`), {
      method: 'GET',
      params: {
        page: 1,
        size: 100,
      },
    });
    return result.result.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function createDynamicRole(data: IIotDynamicRole) {
  try {
    const result = await request(generateAPIPath(`api/v2/dynamicRole`), {
      method: 'POST',
      data,
    });
    return result.result;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function updateDynamicRole(data: IIotDynamicRole) {
  try {
    const result = await request(generateAPIPath(`api/v2/dynamicRole`), {
      method: 'PUT',
      data,
    });
    return result.result;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function removeDynamicRole(data: IIotDynamicRole) {
  try {
    const name = data.name ? data.name : '';
    const result = await generalDelete('iot_dynamic_role', name);
    return result;
  } catch (error) {
    throw error;
  }
}
