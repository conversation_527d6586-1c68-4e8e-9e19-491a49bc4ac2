import {
  DEFAULT_DATE_FORMAT_WITHOUT_TIME,
  DEFAULT_PAGE_SIZE_ALL,
  DOCTYPE_ERP,
} from '@/common/contanst/constanst';
import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { getCropList, getTemplateCropList } from '@/services/cropManager';
import { getCustomerUserList } from '@/services/customerUser';
import {
  getFarmingPlanList,
  getFarmingPlanState,
  getTemplateTaskManagerList,
} from '@/services/farming-plan';
import {
  ProForm,
  ProFormCheckbox,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useIntl, useModel, useRequest } from '@umijs/max';
import { Card, Checkbox, Col, DatePicker, Form, InputNumber, message, Row, Spin } from 'antd';
import { uniqBy } from 'lodash/fp';
import moment from 'moment';
import { FC, ReactNode, useEffect, useMemo, useState } from 'react';
import ProFormTagSelect from '../TagManager/ProFormTagSelect';

interface DetailedInfoProps {
  children?: ReactNode;
  onEditTagSuccess?: () => void;
  onFileListChange?: (fileList: any) => void;
  currentPlanParam?: any;
  setTodoList?: any;
  setTaskItems?: any;
  setWorkTimes?: any;
  setProductions?: any;
  isTemplateTask?: boolean;
  openFromModal?: boolean;
}

const PAGE_SIZE = 20;

const DetailedInfo: FC<DetailedInfoProps> = ({
  children,
  onEditTagSuccess,
  currentPlanParam,
  onFileListChange = () => {},
  setTodoList,
  setTaskItems,
  setWorkTimes,
  setProductions,
  isTemplateTask = false,
  openFromModal = false,
}) => {
  const [isInterval, setIsInterval] = useState(false);
  const [currentPlan, setCurrentPlan] = useState<any>({});
  const [selectedPlan, setSelectedPlan] = useState('');
  const [planStateOptions, setPlanStateOptions] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [taskOptions, setTaskOptions] = useState<any[]>([]);
  const form = ProForm.useFormInstance();
  const cropId = ProForm.useWatch('crop', form);
  const [isTemplate, setIsTemplate] = useState(isTemplateTask);
  const [cropList, setCropList] = useState<any[]>([]);
  const handleTaskSelect = async (taskId: any) => {
    setLoading(true);
    try {
      const filters = [['iot_farming_plan_task', 'name', 'like', taskId]];
      const res = await getTemplateTaskManagerList({ filters, page: 1, size: 1 });
      const task: any = res.data[0];
      const currentValues = form.getFieldsValue(); // Get current form values

      form.setFieldsValue({
        ...currentValues, // Retain existing fields
        // crop: task.crop_id,
        label: task.label,
        status: task.status,
        // farming_plan_state: task.farming_plan_state,
        description: task.description,
        assigned_to: task.assigned_to,
        involved_in_users: task.involve_in_users
          ? task.involve_in_users.map((item: any) => {
              return {
                label:
                  item.first_name || item.last_name
                    ? `${item.first_name || ''} ${item.last_name || ''}`
                    : `${item.email}`,
                customer_user: item.customer_user,
                value: item.name,
              };
            })
          : [],
        tag: task.tag,
      });
      const todoList = task.todo_list
        ? task.todo_list.map((item: any) => {
            return {
              name: item.name,
              label: item.label,
              status: item.status,
              description: item.description,
              is_completed: 0,
              customer_user_id: item.customer_user_id,
              customer_user_name: item.customer_user_name,
            };
          })
        : [];
      setTodoList(todoList);
      const taskItems = task.item_list
        ? task.item_list.map((item: any) => {
            return {
              iot_category_id: item.iot_category_id,
              item_name: item.item_name,
              label: item.label,
              uom_name: item.uom_name,
              description: item.description,
              exp_quantity: item.exp_quantity,
            };
          })
        : [];
      setTaskItems(taskItems);
      const productions = task.prod_quantity_list
        ? task.prod_quantity_list.map((item: any) => {
            return {
              product_id: item.product_id,
              label: item.label,
              item_name: item.item_name,
              uom_name: item.uom_name,
              description: item.description,
              exp_quantity: item.exp_quantity,
            };
          })
        : [];
      setProductions(productions);
    } catch (error) {
      console.log(error);
      message.error('Failed to fetch task details.');
    } finally {
      setLoading(false);
    }
  };

  const { initialState } = useModel(`@@initialState`);
  const currentUser = initialState?.currentUser;
  useEffect(() => {
    onFileListChange(fileList);
  }, [fileList]);

  useEffect(() => {
    form.setFieldValue('farming_plan', currentPlanParam);
    setSelectedPlan(currentPlanParam.name);
    form.setFieldValue('is_template', isTemplateTask);
    setIsTemplate(isTemplateTask);
  }, [currentPlanParam]);

  useEffect(() => {
    form.setFieldValue('assigned_to', currentUser?.user_id);
  }, [currentUser]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!selectedPlan) return;
        const res = await getFarmingPlanState({
          page: 1,
          size: DEFAULT_PAGE_SIZE_ALL,
          filters: [['iot_farming_plan_state', 'farming_plan', 'like', selectedPlan]],
        });
        setPlanStateOptions(
          res.data.map((item: any) => {
            return {
              label: item.label,
              value: item.name,
            };
          }),
        );
        const today = new Date();
        const todayState = await getFarmingPlanState({
          page: 1,
          size: DEFAULT_PAGE_SIZE_ALL,
          filters: [
            ['iot_farming_plan_state', 'farming_plan', 'like', selectedPlan],
            ['iot_farming_plan_state', 'start_date', '<=', today],
            ['iot_farming_plan_state', 'end_date', '>=', today],
          ],
        });
        if (todayState.data.length !== 0) {
          form.setFieldValue('farming_plan_state', todayState?.data?.at(0)?.name);
          form.setFieldValue('start_date', moment(today.toISOString()));
        } else {
          form.setFieldValue('farming_plan_state', res.data.at(0)?.name);
        }
      } catch (error: any) {
        message.error(error.toString());
      }
    };
    fetchData();
    if (currentPlanParam) {
      setCurrentPlan(currentPlanParam);
    }
  }, [selectedPlan]);

  const {
    loading: loadingFarmingPlan,
    run: getFarmingPlanByCrop,
    data,
  } = useRequest(
    ({ cropId }: { cropId: string }) =>
      getFarmingPlanList({
        page: 1,
        size: 1,
        filters: [[DOCTYPE_ERP.iotFarmingPlan, 'crop', '=', cropId]],
      }),
    {
      manual: true,
    },
  );

  const isDisableSelectCrop = useMemo(() => form.getFieldValue('crop'), []);

  const handleChangeCrop = async (v: any) => {
    const res = await getFarmingPlanByCrop({ cropId: v });
    const farmingPlan = res?.[0]?.name;
    form.setFieldValue('farming_plan', farmingPlan);
    setSelectedPlan(farmingPlan);
  };

  useEffect(() => {
    handleChangeCrop(cropId);
  }, [cropId]);

  const intl = useIntl();
  useEffect(() => {
    const fetchCropList = async () => {
      const res = isTemplate
        ? await getTemplateCropList({
            page: 1,
            size: DEFAULT_PAGE_SIZE_ALL,
          })
        : await getCropList({
            page: 1,
            size: DEFAULT_PAGE_SIZE_ALL,
          });

      // form.setFieldValue(
      //   'crop',
      //   uniqBy('name', res.data).map((item: any) => ({
      //     label: item.label,
      //     value: item.name,
      //   })),
      // );
      setCropList(
        uniqBy('name', res.data).map((item: any) => ({
          label: item.label,
          value: item.name,
        })),
      );
    };

    fetchCropList();
  }, [isTemplate]);

  return (
    <Spin spinning={loading}>
      <Row gutter={[5, 5]}>
        <Col md={24}>
          <Card title={intl.formatMessage({ id: 'common.detail' })}>
            <Row gutter={[5, 5]}>
              <Col className="gutter-row" span={12}>
                <FormUploadsPreviewable
                  fileLimit={20}
                  label={intl.formatMessage({ id: 'common.image' })}
                  formItemName={'upload-image'}
                />
              </Col>
              <Col className="gutter-row" span={12}>
                <ProFormSelect
                  name="copy_from_task"
                  label={intl.formatMessage({ id: 'common.copy_from_task' })}
                  showSearch
                  request={async (searchKeys: { keyWords: string }) => {
                    const filters = searchKeys.keyWords
                      ? [['iot_farming_plan_task', 'label', 'like', searchKeys.keyWords]]
                      : undefined;
                    const res = await getTemplateTaskManagerList({
                      filters,
                      page,
                      size: PAGE_SIZE,
                    });
                    return res.data.map((item: any) => ({
                      label: item.label,
                      value: item.name,
                      cropName: item.crop_name,
                      stateName: item.state_name,
                    }));
                  }}
                  onChange={handleTaskSelect}
                  fieldProps={{
                    options: taskOptions,
                    optionLabelProp: 'label',
                    optionRender: (option) => {
                      return (
                        <div>
                          <div>{option.label}</div>
                          <div style={{ fontSize: '12px', color: '#888' }}>
                            {`VM: ${option.data.cropName} - GĐ: ${option.data.stateName}`}
                          </div>
                        </div>
                      );
                    },
                  }}
                />
              </Col>
              <Col span={24}>
                <ProFormCheckbox
                  name="is_template"
                  label={intl.formatMessage({ id: 'common.template_task' })}
                  fieldProps={{
                    onChange: (event) => {
                      setIsTemplate(event.target.checked);
                    },
                  }}
                  disabled={openFromModal}
                />
              </Col>
              <Col span={12}>
                <ProFormText
                  label={intl.formatMessage({ id: 'common.name' })}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  name="label"
                />
              </Col>
              <Col span={12}>
                <ProFormText hidden name={'farming_plan'} />
                <ProFormSelect
                  name="crop"
                  label={intl.formatMessage({
                    id: isTemplate ? 'common.template-crop' : 'common.crop',
                  })}
                  onChange={handleChangeCrop}
                  disabled={isDisableSelectCrop}
                  showSearch
                  // request={async () => {
                  //   return form.getFieldValue('crop');
                  // }}
                  options={cropList}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                />
              </Col>

              <Col span={12}>
                <ProFormSelect
                  label={intl.formatMessage({ id: 'common.state' })}
                  name="farming_plan_state"
                  options={planStateOptions}
                  disabled={planStateOptions.length === 0}
                  fieldProps={{
                    loading: loadingFarmingPlan,
                  }}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                />
              </Col>
              <Col span={12}>
                <ProFormTagSelect onEditTagSuccess={onEditTagSuccess} />
              </Col>
              <Col span={6}>
                <Form.Item
                  label={intl.formatMessage({ id: 'common.start_date' })}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  name="start_date"
                >
                  <DatePicker
                    style={{ width: '100%' }}
                    showTime
                    format={'HH:mm DD/MM/YYYY'}
                  ></DatePicker>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={intl.formatMessage({ id: 'common.end_date' })}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  name="end_date"
                >
                  <DatePicker
                    style={{ width: '100%' }}
                    showTime
                    format={'HH:mm DD/MM/YYYY'}
                  ></DatePicker>
                </Form.Item>
              </Col>
              <Col span={12}>
                <ProFormSelect
                  label={intl.formatMessage({ id: 'common.assigned_to' })}
                  name="assigned_to"
                  request={async () => {
                    const res = await getCustomerUserList({
                      page: 1,
                      size: DEFAULT_PAGE_SIZE_ALL,
                      fields: ['name', 'first_name', 'last_name', 'email'],
                    });
                    return res.data.map((item) => ({
                      label:
                        item.first_name || item.last_name
                          ? `${item.first_name || ''} ${item.last_name || ''}`
                          : `${item.email}`,
                      value: item.name,
                    }));
                  }}
                />
              </Col>
              <Col span={12}>
                <ProFormSelect
                  label={intl.formatMessage({ id: 'common.related_members' })}
                  name="involved_in_users"
                  request={async () => {
                    const res = await getCustomerUserList({
                      page: 1,
                      size: DEFAULT_PAGE_SIZE_ALL,
                      fields: ['name', 'first_name', 'last_name', 'email'],
                    });
                    return res.data.map((item) => ({
                      label:
                        item.first_name || item.last_name
                          ? `${item.first_name || ''} ${item.last_name || ''}`
                          : `${item.email}`,
                      value: item.name,
                    }));
                  }}
                  mode="multiple"
                />
              </Col>
              <Col span={12}>
                <ProFormSelect
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  label={intl.formatMessage({ id: 'common.status' })}
                  name="status"
                  options={[
                    {
                      label: 'Lên kế hoạch',
                      value: 'Plan',
                    },
                    {
                      label: 'Đang xử lý',
                      value: 'In progress',
                    },
                    {
                      label: 'Hoàn tất',
                      value: 'Done',
                    },
                    {
                      label: 'Trì hoãn',
                      value: 'Pending',
                    },
                  ]}
                  initialValue={'Plan'}
                />
              </Col>
              <Col span={24}>
                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item
                      label={intl.formatMessage({ id: 'common.repeat_task' })}
                      name="is_interval"
                      valuePropName="checked"
                    >
                      <Checkbox
                        value={isInterval}
                        onChange={(v) => {
                          setIsInterval(v.target.checked);
                        }}
                      ></Checkbox>
                    </Form.Item>
                  </Col>
                  {isInterval && (
                    <>
                      <Col span={4}>
                        <Form.Item
                          label={intl.formatMessage({ id: 'common.each' })}
                          name="interval_value"
                          initialValue={1}
                          rules={[
                            {
                              required: true,
                            },
                          ]}
                        >
                          <InputNumber style={{ width: '100%' }} min={1}></InputNumber>
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <ProFormSelect
                          style={{ width: '100%' }}
                          name="interval_type"
                          label={intl.formatMessage({ id: 'common.time_type' })}
                          options={[
                            {
                              value: 'd',
                              label: 'Ngày',
                            },
                            {
                              value: 'w',
                              label: 'Tuần',
                            },
                            {
                              value: 'M',
                              label: 'Tháng',
                            },
                          ]}
                          rules={[
                            {
                              required: true,
                            },
                          ]}
                        ></ProFormSelect>
                      </Col>
                      <Col span={8}>
                        <ProFormDateRangePicker
                          style={{ width: '100%' }}
                          label={intl.formatMessage({ id: 'common.interval_range' })}
                          width={'lg'}
                          rules={[
                            {
                              required: true,
                            },
                          ]}
                          fieldProps={{
                            format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
                          }}
                          name="intervalRange"
                        />
                      </Col>
                    </>
                  )}
                </Row>
              </Col>
              <Col md={24}>
                <ProFormTextArea
                  label={intl.formatMessage({ id: 'common.note' })}
                  name="description"
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </Spin>
  );
};

export default DetailedInfo;
