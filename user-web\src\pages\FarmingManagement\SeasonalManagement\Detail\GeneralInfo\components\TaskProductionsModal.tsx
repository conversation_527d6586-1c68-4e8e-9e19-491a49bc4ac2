import { isSubscribedStock } from '@/access';
import { getCropProductionStatisticDetailTask, ICropItemInTask } from '@/services/crop';
import { EyeOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { App, Button, Modal, Space } from 'antd';
import numeral from 'numeral';
import { useState } from 'react';
export interface Props {
  product_id: string;
  product_label: string;
  crop_id: string;
}
const TaskProductionsModal = ({ product_id, product_label, crop_id }: Props) => {
  const [isOpen, setOpen] = useState(false);
  const showModal = () => {
    setOpen(true);
  };

  const { message } = App.useApp();
  const hideModal = () => {
    setOpen(false);
  };

  const handleCancel = () => {
    hideModal();
  };

  const intl = useIntl();
  const columns: ProColumns<ICropItemInTask>[] = [
    {
      title: intl.formatMessage({ id: 'common.task' }),
      dataIndex: 'task_label',
      render(dom, entity, index, action, schema) {
        return (
          <a
            href={`/farming-management/workflow-management/detail/${entity.task_id}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            {entity.task_label}
          </a>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'category.material-management.unit' }),
      dataIndex: 'unit_label',
    },
    {
      title: intl.formatMessage({ id: 'seasonalTab.expected' }),
      dataIndex: 'total_exp_quantity',
      render(dom, entity, index, action, schema) {
        return numeral(entity.total_exp_quantity).format('0,0.00');
      },
    },
    {
      title: intl.formatMessage({ id: 'storage-management.category-management.draft_quantity' }),
      dataIndex: 'total_draft_quantity',
      render(dom, entity, index, action, schema) {
        return numeral(entity.total_draft_quantity).format('0,0.00');
      },
    },
    // {
    //   title: intl.formatMessage({ id: 'seasonalTab.used' }),
    //   dataIndex: 'total_quantity',
    //   render(dom, entity, index, action, schema) {
    //     return numeral(entity.total_quantity).format('0,0.00');
    //   },
    // },
    // {
    //   title: intl.formatMessage({ id: 'seasonalTab.reality' }),
    //   dataIndex: 'total_real_quantity',
    //   render(dom, entity, index, action, schema) {
    //     return numeral(entity.total_real_quantity).format('0,0.00');
    //   },
    // },
    {
      title: intl.formatMessage({ id: 'seasonalTab.issue' }),
      dataIndex: 'total_issued_quantity',
      render(dom, entity, index, action, schema) {
        return numeral(entity.total_issued_quantity).format('0,0.00');
      },
      hideInTable: !isSubscribedStock(),
    },

    {
      title: intl.formatMessage({ id: 'common.harvested_quantity' }),
      dataIndex: 'total_finished_quantity',
      render(dom, entity, index, action, schema) {
        return numeral(entity.total_finished_quantity).format('0,0.00');
      },
      hideInTable: !isSubscribedStock(),
    },
  ];
  return (
    <>
      <Space size={'small'}>
        <Button icon={<EyeOutlined />} onClick={showModal}></Button>
        <span>{product_label}</span>
      </Space>
      <Modal
        title={`Chi tiết sản lượng ${product_label} trong vụ mùa`}
        open={isOpen}
        onCancel={handleCancel}
        footer={null}
        width={1200}
      >
        <ProTable<ICropItemInTask>
          columns={columns}
          search={false}
          pagination={{
            pageSizeOptions: [10, 20, 50, 100],
            showSizeChanger: true,
            defaultPageSize: 10,
          }}
          request={async (params, sorter, filter) => {
            try {
              const res = await getCropProductionStatisticDetailTask({
                page: params.current,
                size: params.pageSize,
                crop_id,
                product_id,
              });
              return {
                data: res.data,
                success: true,
              };
            } catch (error: any) {
              message.error(`Lỗi khi kéo dữ liệu: ${error.message}`);
              return {
                success: false,
              };
            }
          }}
          rowKey={'task_id'}
        />
      </Modal>
    </>
  );
};

export default TaskProductionsModal;
