import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import FormUploadFiles from '@/components/UploadFIles';
import { useUpdateDeliveryNote } from '@/pages/WarehouseManagementV3/hooks/useUpdateDeliveryNote';
import { getDeliveryNoteDetail } from '@/services/stock/deliveryNote';
import { formatMoneyNumeral, formatNumeral, openInNewTab } from '@/services/utils';
import { IDeliveryNoteDetailItem } from '@/types/deliveryNote.type';
import { PrinterOutlined } from '@ant-design/icons';
import {
  ActionType,
  ProColumns,
  ProForm,
  ProFormDatePicker,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { FormattedMessage, useAccess, useIntl, useRequest } from '@umijs/max';
import { Button, Col, Divider, Modal, Row, Spin } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useRef, useState } from 'react';

interface Props {
  name: string;
  isModalOpen: boolean;
  setIsModalOpen: any;
}
const ExportReceiptDetail = ({ name, isModalOpen, setIsModalOpen }: Props) => {
  const actionRef = useRef<ActionType>();
  const [form] = useForm();
  const [items, setItems] = useState<IDeliveryNoteDetailItem[]>([]);
  const [fileList, setFileList] = useState<string | undefined>(undefined);

  const { data, loading, refresh, run } = useRequest(getDeliveryNoteDetail, {
    manual: true,
    onError(error) {
      console.log('error', error.message);
    },
    onSuccess(data, params) {
      setFileList(data.file_path);
    },
  });
  const handleReload = () => {
    actionRef.current?.reload();
    refresh();
  };
  const access = useAccess();
  const canReadInventoryValue = access.canReadCategoryInventoryFieldLevelManagement();
  const columns: ProColumns<IDeliveryNoteDetailItem>[] = [
    {
      title: <FormattedMessage id="common.index" />,
      dataIndex: 'index',
      render(dom, entity, index, action, schema) {
        return <div>{index + 1}</div>;
      },
      width: 15,
    },
    {
      title: <FormattedMessage id="warehouse-management.export-history.item_id" />,
      dataIndex: 'item_name',
      width: 20,
    },
    {
      title: <FormattedMessage id="warehouse-management.export-history.item_label" />,
      dataIndex: 'item_label',
      width: 20,
    },
    {
      title: <FormattedMessage id="common.unit" />,
      dataIndex: 'uom_name',
      width: 20,
    },
    {
      title: <FormattedMessage id="common.quantity" />,
      dataIndex: 'qty',
      width: 20,
      render(dom, entity, index, action, schema) {
        return <div>{formatNumeral(entity.qty)}</div>;
      },
    },
    {
      title: <FormattedMessage id="warehouse-management.export-history.rate" />,
      dataIndex: 'rate',
      width: 20,
      render(dom, entity, index, action, schema) {
        return <div>{formatMoneyNumeral(entity.rate)}</div>;
      },
      hideInTable: !canReadInventoryValue,
    },
    {
      title: <FormattedMessage id="warehouse-management.export-history.amount" />,
      dataIndex: 'amount',
      width: 20,
      render(dom, entity, index, action, schema) {
        return <div>{formatMoneyNumeral(entity.amount)}</div>;
      },
      hideInTable: !canReadInventoryValue,
    },
  ];

  useEffect(() => {
    run({ name });
  }, [name]);
  useEffect(() => {
    form.resetFields();
    form.setFieldsValue(data);
    form.setFieldValue('warehouse_label', data?.items?.at(0)?.warehouse_label);
    form.setFieldValue('user', `${data?.user_first_name} ${data?.user_last_name}`);
    setItems(data?.items || []);
  }, [data]);
  const { formatMessage } = useIntl();

  const { run: update, loading: updating } = useUpdateDeliveryNote();

  return (
    <Modal
      open={isModalOpen}
      title={<FormattedMessage id={'warehouse-management.export-history.detail'} />}
      onCancel={() => {
        setIsModalOpen(false);
        handleReload();
      }}
      footer={[]}
      width={1000}
    >
      <ProForm submitter={false} disabled form={form} layout="vertical" grid={true}>
        <ProFormText
          label={<FormattedMessage id={'warehouse-management.export-history.id'} />}
          colProps={{
            sm: 24,
            md: 8,
          }}
          name={'name'}
          width="md"
        />
        <ProFormDatePicker
          label={<FormattedMessage id={'warehouse-management.export-history.date'} />}
          colProps={{
            sm: 24,
            md: 8,
          }}
          name={'posting_date'}
          width="md"
          fieldProps={{
            format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
          }}
        />
        <ProFormText
          label={<FormattedMessage id={'warehouse-management.export-history.customer'} />}
          colProps={{
            sm: 24,
            md: 8,
          }}
          name={'customer_label'}
          width={'md'}
        />
        <ProFormTextArea
          label={<FormattedMessage id={'common.description'} />}
          colProps={{
            sm: 24,
            md: 8,
          }}
          name={'description'}
          width="md"
        />
        <ProFormText
          label={<FormattedMessage id={'common.assigned_to'} />}
          colProps={{
            sm: 24,
            md: 8,
          }}
          name={'user'}
          width="md"
        />
        <ProFormText
          label={<FormattedMessage id={'warehouse-management.export-history.warehouse_label'} />}
          colProps={{
            sm: 24,
            md: 8,
          }}
          name={'warehouse_label'}
          width="md"
        />
      </ProForm>
      <div
        style={{
          marginLeft: 5,
          maxWidth: 500,
        }}
      >
        <Row gutter={16}>
          <Col span={10}>
            <Spin spinning={updating}>
              <FormUploadFiles
                maxSize={10}
                isReadonly={false}
                initialImages={fileList}
                formItemName={'file_path'}
                label={formatMessage({
                  id: 'common.form.document',
                })}
                fileLimit={20}
                onValueChange={async (value) => {
                  await update({
                    name,
                    file_path: value,
                  });
                }}
              />
            </Spin>
          </Col>
          <Col span={12}>
            <Button
              key={'download'}
              icon={<PrinterOutlined />}
              onClick={() =>
                openInNewTab(`/warehouse-management-v3/to-pdf?type=export&id=${data?.name}`)
              }
            >
              {<FormattedMessage id={'common.print_receipt'} />}
            </Button>
          </Col>
        </Row>
      </div>

      <br />
      <Divider />
      <ProTable<IDeliveryNoteDetailItem>
        columns={columns}
        cardBordered
        size="small"
        dataSource={items}
        rowKey={'name'}
        search={false}
      />
      <Divider />
      <Row>
        <Col span={4} offset={16}>
          <FormattedMessage id="warehouse-management.export-history.total_quantity" />
        </Col>
        <Col span={4}>{formatNumeral(data?.total_qty)}</Col>
      </Row>
      <Row gutter={[0, 12]}>
        <Col span={4} offset={16}>
          <FormattedMessage id="warehouse-management.export-history.total_price" />
        </Col>
        <Col span={4}>{formatMoneyNumeral(data?.net_total)}</Col>
      </Row>
    </Modal>
  );
};

export default ExportReceiptDetail;
