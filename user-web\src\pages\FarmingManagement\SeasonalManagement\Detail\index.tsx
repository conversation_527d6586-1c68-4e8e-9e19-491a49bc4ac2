import { myLazy } from '@/utils/lazy';
import { DescriptionsSkeleton, ListSkeleton, TableSkeleton } from '@ant-design/pro-components';
import { Card, Tabs } from 'antd';
import { FC, ReactNode, useState } from 'react';

import FallbackComponent from '@/components/FallbackContent';
import PageContainerTabsWithSearch from '@/components/PageContainerTabsWithSearch';
import TodayTasks from '@/components/TodayTasks';
import TodayTemplateTask from '@/components/TodayTemplateTasks';
import { getCrop } from '@/services/crop';
import { Access, useAccess, useIntl, useModel, useRequest } from '@umijs/max';
import { nanoid } from 'nanoid';
import TableCropPlanDetail from '../../CropPlan/components/TableCropPlanDetails';
import TaskTemplateTable from '../../CropPlan/components/TaskTemplateTable';
import CropPlanDetail from '../../CropPlan/Detail';
import CropPlanTemplateDetail from '../../CropPlan/TemplateDetail';
import useParamsUrl from './hooks/useParamsUrl';
import Instruct from './Instruct';
import CreateNoteModal from './Note/Create';
import CreatePandemicModal from './Pandemic/Create';
import TicketList from './Ticket';

const Pandemic = myLazy(() => import('./Pandemic'));
const GeneralInfo = myLazy(() => import('./GeneralInfo'));
const SeasonalNote = myLazy(() => import('./Note'));
const Participants = myLazy(() => import('./Participants'));
interface DetailWorkflowProps {
  children?: ReactNode;
}
const DetailWorkflow: FC<DetailWorkflowProps> = () => {
  const { detailId, tabActive, setTabActive } = useParamsUrl();
  const { selectedCrop, setSelectedCrop } = useModel('MyCrop');
  const intl = useIntl();
  //  hướng dẫn cần thông tin plant_id nên get ở đây luôn
  const { data: detailCrop, loading } = useRequest(
    () =>
      getCrop({
        page: 1,
        size: 1,
        filters: JSON.stringify([['iot_crop', 'name', 'like', detailId]]),
        fields: JSON.stringify(['name', 'plant_id', 'is_template']),
      }),
    {
      refreshDeps: [detailId],
      formatResult(res: any) {
        setSelectedCrop(res.data.data[0]);
        return res.data.data[0];
      },
    },
  );
  // refresh the Pandemic
  const [pandemicCacheKey, setPandemicCacheKey] = useState(nanoid());
  const [cropNoteCacheKey, setCropNoteCacheKey] = useState(nanoid());
  const access = useAccess();
  const canReadTask = access.canAccessPageWorkFlowManagement();
  return (
    <PageContainerTabsWithSearch
      tabsItems={[
        {
          label: intl.formatMessage({ id: 'seasonalTab.overview' }),
          component: () => <GeneralInfo cropId={detailId as string} />,
          // extraPage: [<Button key={'cancel'}>Hủy</Button>, <Button key={'save'}>Lưu</Button>],
          fallback: <DescriptionsSkeleton active />,
        },
        {
          label: intl.formatMessage({ id: 'seasonalTab.care' }),
          component: () => (
            <Access accessible={canReadTask} fallback={<FallbackComponent />}>
              <Card>
                <Tabs
                  defaultActiveKey="1"
                  items={[
                    {
                      label: intl.formatMessage({ id: 'common.all' }),
                      children: !selectedCrop.is_template ? (
                        <TableCropPlanDetail cropId={detailId} createNewTaskInModal />
                      ) : (
                        <TaskTemplateTable cropId={detailId} createNewTaskInModal />
                      ),
                      key: '1',
                    },
                    {
                      label: intl.formatMessage({ id: 'common.today' }),
                      children: !selectedCrop.is_template ? (
                        <TodayTasks cropId={detailId} />
                      ) : (
                        <TodayTemplateTask cropId={detailId} />
                      ),
                      key: '2',
                    },
                    {
                      label: intl.formatMessage({ id: 'common.plan' }),
                      children: !selectedCrop.is_template ? (
                        <CropPlanDetail />
                      ) : (
                        <CropPlanTemplateDetail />
                      ),
                      key: '3',
                    },
                  ]}
                />
              </Card>
            </Access>
          ),
        },
        {
          label: intl.formatMessage({ id: 'seasonalTab.health' }),
          component: () => <Pandemic cropId={detailId} cacheKey={pandemicCacheKey} />,
          extraPage: [
            // <Button key={'delete-ss'}>Xóa vụ mùa</Button>,
            // <Button key={'create-ss'}>Tạo vụ mùa mới</Button>,
            <CreatePandemicModal
              key="create-pandemic"
              cropId={detailId}
              onSuccess={() => {
                setPandemicCacheKey(nanoid());
              }}
            />,
          ],
          fallback: <ListSkeleton size={10} />,
        },
        {
          label: intl.formatMessage({ id: 'seasonalTab.note' }),
          component: () => <SeasonalNote cropId={detailId} cacheKey={cropNoteCacheKey} />,
          extraPage: [
            // <Button key={'delete-ss'}>Xóa vụ mùa</Button>,
            // <Button key={'create-ss'}>Tạo vụ mùa mới</Button>,
            <CreateNoteModal
              key="create-note"
              cropId={detailId}
              onSuccess={() => {
                setCropNoteCacheKey(nanoid());
              }}
            />,
          ],
          fallback: <ListSkeleton size={10} />,
        },
        {
          label: intl.formatMessage({ id: 'seasonalTab.plantingInformation' }),
          component: () => <Instruct plantId={detailCrop?.plant_id} />,
        },
        // {
        //   label: intl.formatMessage({ id: 'seasonalTab.cultivationDiary' }),
        //   component: () => <LogOverview />,
        // },
        {
          label: intl.formatMessage({ id: 'seasonalTab.participants' }),
          component: () => <Participants cropId={detailId as string} />,
          fallback: <TableSkeleton active size={10} />,
        },
        {
          label: intl.formatMessage({ id: 'common.ticket' }),
          component: () => <TicketList cropId={detailId as string} />,
          fallback: <TableSkeleton active size={10} />,
        },
      ]}
      autoFormatTabKey
    />
  );
};

export default DetailWorkflow;
