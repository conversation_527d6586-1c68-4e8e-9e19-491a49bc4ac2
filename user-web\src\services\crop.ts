import { request } from '@umijs/max';
import { generateAPIPath } from './utils';
const CRUD_PATH = {
  CREATE: 'crop',
  READ: 'crop',
  UPDATE: 'crop',
  DELETE: 'crop',
};
export type ICropManagerInfo = {
  name: string;
  label: string;
  plant_id: string;
  plant_name: string;
  zone_id: string;
  zone_name: string;
  project_id: string;
  project_name: string;
  start_date: string | null;
  end_date: string | null;
  status: string | null;
};

export const getCropManagementInfoList = async (params: any) => {
  const res = await request<API.ResponseResult<ICropManagerInfo[]>>(
    generateAPIPath('api/v2/cropManage/crop-management-info'),
    {
      method: 'GET',
      params: params,
    },
  );
  return {
    data: res.result,
  };
};

export const getCropByTask = async (params: { task_id: string }) => {
  const res = await request<
    API.ResponseResult<{ name: string; label: string; task_id: string; task_label: string }>
  >(generateAPIPath('api/v2/cropManage/crop-by-task'), {
    method: 'GET',
    params: params,
  });
  return {
    data: res.result,
  };
};

export async function cropList({
  page = 0,
  size = 20,
  fields = ['*'],
  filters = [],
  or_filters = [],
  order_by = '',
  group_by = '',
}: {
  page?: number;
  size?: number;
  fields?: string[];
  filters?: any;
  or_filters?: any;
  order_by?: string;
  group_by?: string;
}): Promise<{
  data: any[];
}> {
  try {
    const params = {
      page,
      size,
      fields: JSON.stringify(fields),
      filters: JSON.stringify(filters),
      or_filters: JSON.stringify(or_filters),
      // order_by,
      // group_by
    };
    const result = await request(generateAPIPath(`api/v2/cropManage/${CRUD_PATH.READ}`), {
      method: 'GET',
      params: params,
      queryParams: params,
    });
    return result.result;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export const getCropNote = async (params: any) => {
  // const stringParam = JSON.stringify(params)
  const res = await request<API.ResponseResult<any[]>>(generateAPIPath(`api/v2/cropManage/note`), {
    method: 'GET',
    params: params,
  });
  return {
    data: res.result,
  };
};

export const getCropPest = async (params: any) => {
  const res = await request<API.ResponseResult<any[]>>(generateAPIPath(`api/v2/cropManage/pest`), {
    method: 'GET',
    params: params,
  });
  console.log(' res.result', res.result);
  return {
    data: res.result,
  };
};

export const getCrop = async (params: any) => {
  const res = await request<API.ResponseResult<any[]>>(generateAPIPath('api/v2/cropManage/crop'), {
    method: 'GET',
    params: params,
  });
  return {
    data: res.result,
  };
};

export interface ICropItemStatistic {
  category_name: string;
  category_label: string;
  category_group_id: string;
  category_group_label: string;
  valuation_rate?: number;
  total_exp_quantity: number;
  total_draft_quantity: number;
  total_quantity: number;
  total_real_quantity: number;
  total_loss_quantity: number;
  remain_quantity: number;
  unit_label: string;
}

export const getCropItemStatistic = async (params: API.ListParamsReq & { crop_id: string }) => {
  const res = await request<API.ResponseResult<ICropItemStatistic[]>>(
    generateAPIPath('api/v2/cropManage/statisticItem'),
    {
      method: 'GET',
      params,
    },
  );
  return {
    data: res.result,
  };
};

export interface ICropItemInTask {
  task_id: string;
  task_label: string;
  start_date: string;
  end_date: string;
  state_label: string;
  plan_label: string;
  category_id: string;
  category_label: string;
  category_group_id: string;
  category_group_label: string;
  total_exp_quantity: number;
  total_quantity: number;
  total_real_quantity: number;
  total_issued_quantity: number;
  total_loss_quantity: number;
  total_finished_quantity: number;
  unit_label: string;
  unit_id: string;
  total_draft_quantity: number;
}
export const getCropItemStatisticDetailTask = async (
  params: API.ListParamsReq & { crop_id: string; category_id: string },
) => {
  const res = await request<API.PaginationResponseResult<ICropItemInTask[]>>(
    generateAPIPath('api/v2/cropManage/statisticItem/detail-in-crop'),
    {
      method: 'GET',
      params,
    },
  );
  return {
    data: res.result.data,
    pagination: res.result.pagination,
  };
};

export interface ICropProductionInTask {
  task_id: string;
  task_label: string;
  start_date: string;
  end_date: string;
  state_label: string;
  plan_label: string;
  product_id: string;
  product_label: string;
  product_group_id: string;
  product_group_label: string;
  total_exp_quantity: number;
  total_quantity: number;
  total_real_quantity: number;
  total_issued_quantity: number;
  total_loss_quantity: number;
  unit_label: string;
  unit_id: string;
}

export const getCropProductionStatisticDetailTask = async (
  params: API.ListParamsReq & { crop_id: string; product_id: string },
) => {
  const res = await request<API.PaginationResponseResult<ICropItemInTask[]>>(
    generateAPIPath('api/v2/cropManage/statisticProductQuantity/detail-in-crop'),
    {
      method: 'GET',
      params,
    },
  );
  return {
    data: res.result.data,
    pagination: res.result.pagination,
  };
};

export const getCropParticipantsStatistic = async (
  params: API.ListParamsReq & { crop_id: string },
) => {
  const res = await request<API.PaginationResponseResult<ICropItemInTask[]>>(
    generateAPIPath('api/v2/cropManage/statisticParticipant'),
    {
      method: 'GET',
      params,
    },
  );
  return {
    data: res.result.data,
    pagination: res.result.pagination,
  };
};

export const getCropParticipantsTaskList = async (
  params: API.ListParamsReq & { crop_id: string; customer_user_id: string },
) => {
  const res = await request<API.PaginationResponseResult<ICropItemInTask[]>>(
    generateAPIPath('api/v2/cropManage/statisticParticipant/detail-task-list'),
    {
      method: 'GET',
      params,
    },
  );
  return {
    data: res.result.data,
    pagination: res.result.pagination,
  };
};

export interface ICropProductionQuantityStatistic {
  agri_product_name: string;
  agri_product_label: string;
  total_exp_quantity: number;
  total_draft_quantity: number;
  total_quantity: number;
  total_real_quantity: number;
  total_finished_quantity: number;
  valuation_rate: number;
  unit_label: string;
}
export const getCropProductionQuantityStatistic = async (
  params: API.ListParamsReq & { crop_id: string },
) => {
  const res = await request<API.ResponseResult<ICropProductionQuantityStatistic[]>>(
    generateAPIPath('api/v2/cropManage/statisticProductQuantity'),
    {
      method: 'GET',
      params,
    },
  );
  return {
    data: res.result,
  };
};

export interface ICropWorksheetStatistic {
  work_type_label: string;
  total_exp_quantity: number;
  total_quantity: number;
  cost: number;
  type: string;
}

export const getCropWorksheetStatistic = async (
  params: API.ListParamsReq & { crop_id: string },
) => {
  const res = await request<API.ResponseResult<ICropWorksheetStatistic[]>>(
    generateAPIPath('api/v2/cropManage/statisticWorksheet'),
    {
      method: 'GET',
      params,
    },
  );
  return {
    data: res.result.map((stat) => ({
      ...stat,
      type: stat.type.toLowerCase() === 'hour' ? 'Giờ' : 'Công',
    })),
  };
};
