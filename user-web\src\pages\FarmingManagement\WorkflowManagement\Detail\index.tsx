import { DEFAULT_DATE_AND_HH_MM_FORMAT } from '@/common/contanst/constanst';
import ItemUsedTableUpdateView from '@/components/Task/TaskItemUsed/ItemUsedTableUpdateView';
import ProductionTableUpdateView from '@/components/Task/TaskProductionNew/ProductionTableUpdateView';
import {
  createFarmingPlanTaskAssignUser,
  getAllTaskManagerList,
  updateFarmingPlanTask,
} from '@/services/farming-plan';
import { generalDelete } from '@/services/sscript';
import { removeTask } from '@/services/TaskAndTodo';
import { PageContainer, ProForm } from '@ant-design/pro-components';
import { FormattedMessage, history, useAccess, useIntl, useParams } from '@umijs/max';
import { App, Button, Drawer, message, Skeleton, Space } from 'antd';
import moment from 'moment';
import { nanoid } from 'nanoid';
import { FC, ReactNode, useEffect, useState } from 'react';
import DetailedInfo from './DetailedInfo';
import TaskChild from './TaskChild';

interface DetailWorkflowProps {
  children?: ReactNode;
  mode?: 'normal' | 'modal';
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onEditSuccess?: () => void;
  taskIdProps?: string | null;
}
type IFormData = {};

const DetailWorkflow: FC<DetailWorkflowProps> = ({
  mode,
  open,
  onOpenChange,
  onEditSuccess,
  taskIdProps,
}) => {
  const [todoList, setTodoList] = useState([]);
  const [rawInvoled, setRawInvoled] = useState([]);

  const [loading, setLoading] = useState(false);
  const [taskData, setTaskData] = useState<any>({});

  const [form] = ProForm.useForm();

  const location = history.location;
  const pathArr = location.pathname?.split('/');
  const taskId = taskIdProps || pathArr[pathArr.length - 1];
  const getTaskData = async () => {
    try {
      setLoading(true);

      const resData = await getAllTaskManagerList({
        filters: [['iot_farming_plan_task', 'name', 'like', taskId]],
      });

      const rawInvoled: any = resData.data[0].involve_in_users
        ? resData.data[0].involve_in_users
        : [];
      setRawInvoled(rawInvoled);
      const task: any = resData.data[0];
      task.involve_in_users = rawInvoled.map((d: any) => d.customer_user);
      task.dateRange = [moment(task.start_date), moment(task.end_date)];
      task.start_date = moment(task.start_date);
      task.end_date = moment(task.end_date);
      // const res = await getFarmingPlanState({
      //   page: 1,
      //   size: DEFAULT_PAGE_SIZE_ALL,
      //   filters: [['iot_farming_plan_state', 'name', 'like', task.farming_plan_state]],
      // });

      // task.farming_plan = res.data[0]?.farming_plan || '';
      setTaskData(task);
      form.setFieldsValue(task);
    } catch (error: any) {
      message.error(error.toString());
    } finally {
      setLoading(false);
    }
  };
  const [reloadId, setReloadId] = useState(nanoid());
  useEffect(() => {
    getTaskData();
  }, [taskId, reloadId]);
  console.log('taskdata', taskData);
  const { id } = useParams();

  const { modal } = App.useApp();
  const intl = useIntl();

  const onFinish = async (values: any) => {
    try {
      setLoading(true);
      values.start_date = moment(values.start_date, DEFAULT_DATE_AND_HH_MM_FORMAT).format(
        'YYYY-MM-DD HH:mm:ss',
      );
      values.end_date = moment(values.end_date, DEFAULT_DATE_AND_HH_MM_FORMAT).format(
        'YYYY-MM-DD HH:mm:ss',
      );
      values.name = taskId;
      await updateFarmingPlanTask({
        ...values,
      });
      const involve_in_users = values.involve_in_users;
      console.log('involve_in_users', involve_in_users);
      let removeArr: any = rawInvoled.filter((d: any) => {
        return !involve_in_users.find((el: string) => d.customer_user === el);
      });
      console.log('removeArr', removeArr);

      let createArr = involve_in_users.filter((d: string) => {
        return !rawInvoled.find((el: any) => el.customer_user === d);
      });
      console.log('createArr', createArr);
      for (let i = 0; i < removeArr.length; i++) {
        await generalDelete('iot_assign_user', removeArr[i].name);
      }

      for (let i = 0; i < createArr.length; i++) {
        await createFarmingPlanTaskAssignUser({
          customer_user: createArr[i],
          task: taskId,
        });
      }

      message.success(intl.formatMessage({ id: 'common.success' }));
      setReloadId(nanoid());
      onEditSuccess?.();
    } catch (error: any) {
      message.error(error.toString());
    } finally {
      setLoading(false);
    }
  };

  const onDelete = () => {
    modal.confirm({
      title: intl.formatMessage({ id: 'common.confirm_delete_title' }),
      content: intl.formatMessage({ id: 'common.confirm_delete_content' }),
      okText: intl.formatMessage({ id: 'common.confirm_delete_ok' }),
      okButtonProps: {
        danger: true,
      },
      onOk: async () => {
        try {
          await removeTask(taskId);
          message.success(intl.formatMessage({ id: 'common.success' }));
          history.push('/farming-management/workflow-management');
        } catch (error: any) {
          message.error(error.toString());
        }
      },
    });
  };

  const handleImageChange = (imageUrl: string) => {
    form.setFieldValue('image', imageUrl);
  };

  const access = useAccess();
  const canDeleteTask = access.canDeleteInWorkFlowManagement();
  const [submitting, setSubmitting] = useState(false);
  const extraButtons = [];
  if (canDeleteTask) {
    extraButtons.push(
      <Button
        type="primary"
        danger
        key="cancel"
        onClick={onDelete}
        disabled={!access.canDeleteAllInPageAccess()}
      >
        <FormattedMessage id="common.delete_task" />
      </Button>,
    );
  }
  const content = (
    <ProForm<IFormData> form={form} onFinish={onFinish} submitter={false} grid={true}>
      <Space
        size={'large'}
        direction="vertical"
        style={{
          width: '100%',
        }}
      >
        <DetailedInfo
          task_id={taskId}
          loading={loading}
          imageUrl={form.getFieldValue('image')}
          setImageUrl={handleImageChange}
          farming_plan={taskData.farming_plan}
          onEditTagSuccess={onEditSuccess}
        />
        <TaskChild task_id={taskId} />
        <ItemUsedTableUpdateView task_id={taskId} isTemplateTask={taskData.is_template} />
        <ProductionTableUpdateView task_id={taskId} isTemplateTask={taskData.is_template} />
      </Space>
    </ProForm>
  );
  const footer = [
    <Space key="footer">
      <Button
        key="cancel"
        onClick={() => {
          if (mode === 'modal') {
            onOpenChange?.(false);
            return;
          }
          history.back();
        }}
      >
        <FormattedMessage id="common.cancel" />
      </Button>
      <Button
        onClick={() => {
          form.submit();
        }}
        loading={submitting}
        key="save"
        type="primary"
      >
        <FormattedMessage id="common.save" />
      </Button>
    </Space>,
  ];
  if (mode === 'modal')
    return (
      <Drawer
        width={1500}
        title={intl.formatMessage({ id: 'task.create_new_task' })}
        open={open}
        onClose={() => {
          onOpenChange?.(false);
        }}
        footer={footer}
        extra={extraButtons}
      >
        {content}
      </Drawer>
    );
  if (loading) {
    return <Skeleton active />;
  }
  return (
    <PageContainer fixedHeader extra={extraButtons}>
      {content}
    </PageContainer>
  );
};

export default DetailWorkflow;
