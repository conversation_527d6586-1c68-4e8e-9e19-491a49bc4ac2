import FallbackComponent from '@/components/FallbackContent';
import TodayTasks from '@/components/TodayTasks';
import { myLazy } from '@/utils/lazy';
import { DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Access, FormattedMessage, Link, useAccess, useIntl } from '@umijs/max';
import { Button, Card, Tabs } from 'antd';
import { FC, ReactNode, Suspense } from 'react';
import TableCropPlanDetail from '../CropPlan/components/TableCropPlanDetails';
import TableTaskTemplateTableDetail from '../CropPlan/components/TaskTemplateTable';
const CalendarTask = myLazy(() => import('./CalendarTask'));

interface WorkflowManagementProps {
  children?: ReactNode;
}

const WorkflowManagement: FC<WorkflowManagementProps> = ({ children }) => {
  const access = useAccess();
  const canCreateTask = access.canCreateInWorkFlowManagement(); // Check access here
  const intl = useIntl();
  const extraButtons = [
    <Button key="22" icon={<UploadOutlined />}>
      {''}
      <FormattedMessage id="action.export" />
    </Button>,
    <Button key="222" icon={<DownloadOutlined />}>
      {''}
      <FormattedMessage id="action.import" />
    </Button>,
  ];
  if (canCreateTask) {
    extraButtons.push(
      <Link key="2" to="/farming-management/workflow-management/create">
        <Button icon={<PlusOutlined />} type="primary">
          {''}
          <FormattedMessage id="workflowTab.createWork" />
        </Button>
      </Link>,
    );
  }
  return (
    <Access accessible={access.canAccessPageWorkFlowManagement()} fallback={<FallbackComponent />}>
      <PageContainer fixedHeader extra={extraButtons}>
        <Card>
          <Tabs
            defaultActiveKey="1"
            items={[
              {
                label: intl.formatMessage({ id: 'workflowTab.today' }),
                children: <TodayTasks />,
                key: '1',
              },
              {
                label: intl.formatMessage({ id: 'workflowTab.all' }),
                children: <TableCropPlanDetail />,
                key: '2',
              },
              {
                label: intl.formatMessage({ id: 'common.template_task' }),
                children: <TableTaskTemplateTableDetail />,
                key: '3',
              },
              {
                label: intl.formatMessage({ id: 'workflowTab.workSchedule' }),
                children: (
                  <Suspense fallback={<Card loading />}>
                    <CalendarTask />
                  </Suspense>
                ),
                key: '4',
              },
            ]}
          />
        </Card>
      </PageContainer>
    </Access>
  );
};

export default WorkflowManagement;
