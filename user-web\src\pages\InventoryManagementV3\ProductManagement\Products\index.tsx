import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getProductItemV3, ProductItemV3 } from '@/services/InventoryManagementV3/product-item';
import { generateAPIPath, getParamsReqTable } from '@/services/utils';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { FormattedMessage, history, useAccess, useModel } from '@umijs/max';
import { Button, Space } from 'antd';
import numeral from 'numeral';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import CreateProduct from './CreateProduct-v2';
import DeleteProduct from './DeleteProduct';
import UpdateProduct from './EditProduct-v2';
// import CreateItem from './Components/CreateItem';
// import DeleteItem from './Components/DeleteItem';
// import UpdateItem from './Components/UpdateItem';
// import UnitAndPacking from './UnitAndPacking';

export type TableListItem = {
  key: number;
  name: string;
  status: string;
  updatedAt: number;
  createdAt: number;
  money: number;
};
type ProductsTableProps = {
  categoryName?: string;
};
const ProductsTable: React.FC<ProductsTableProps> = ({ categoryName }) => {
  const [isModalOpen, setModalOpen] = useState(false);
  const [currentItemId, setCurrentItemId] = useState<string | undefined>(undefined);

  const tableRef = useRef<ActionType>();
  const reloadTable = async () => {
    tableRef.current?.reload();
  };
  useEffect(() => {
    reloadTable();
  }, [categoryName]);

  const handleLabelClick = (id: string) => {
    setCurrentItemId(id);
    setModalOpen(true);
  };
  const access = useAccess();
  const canCreateCategory = access.canCreateInCategoryManagement();
  const canUpdateCategory = access.canUpdateInCategoryManagement();
  const canDeleteCategory = access.canDeleteInCategoryManagement();

  const toolBarRender: any = [];
  if (canCreateCategory) {
    toolBarRender.push(
      // <UnitAndPacking refreshFnc={reloadTable} key={'unit'} />,
      <Button
        key={'uom'}
        onClick={() => {
          history.push('/inventory-management-v3/uom');
        }}
      >
        <FormattedMessage id="common.uom" defaultMessage="UOM" />
      </Button>,
      <CreateProduct onSuccess={reloadTable} key="create" />,
    );
  }

  const columns: ProColumns<ProductItemV3>[] = useMemo(
    () => [
      {
        title: <FormattedMessage id="category.material-management.category_name" />,
        dataIndex: 'label',
        width: 200,
        render: (dom, entity) => (
          <a onClick={() => handleLabelClick(entity.name)}>{entity.label}</a>
        ),
        sorter: true,
        fixed: 'left',
      },
      {
        title: <FormattedMessage id="common.item_name" />,
        dataIndex: 'item_name',
        width: 150,
        sorter: true,
      },
      {
        title: <FormattedMessage id="common.form.image" />,
        dataIndex: 'image',
        search: false,
        sorter: true,
        width: 100,
        render: (dom, entity) =>
          entity.image ? (
            <img
              width={'70px'}
              src={generateAPIPath('api/v2/file/download?file_url=' + entity.image)}
            ></img>
          ) : (
            ''
          ),
        // fixed: 'left',
      },
      {
        title: <FormattedMessage id="category.material-management.category_type" />,
        dataIndex: 'item_group_label',
        width: 150,
        search: false,
        sorter: true,
      },
      {
        title: <FormattedMessage id="category.material-management.unit" />,
        dataIndex: 'uom_label',
        width: 100,
        search: false,
        sorter: true,
      },
      {
        title: <FormattedMessage id="common.valuation_rate" />,
        dataIndex: 'valuation_rate',
        width: 120,
        render(dom, entity) {
          return <>{numeral(entity.valuation_rate).format('0,0')}</>;
        },
        search: false,
        sorter: true,
      },
      {
        title: <FormattedMessage id="common.standard_rate" />,
        dataIndex: 'standard_rate',
        width: 120,
        render(dom, entity) {
          return <>{numeral(entity.standard_rate).format('0,0')}</>;
        },
        search: false,
        sorter: true,
      },
      {
        title: 'Action',
        dataIndex: 'name',
        search: false,
        width: 100,
        fixed: 'right',
        render: (dom, entity) => (
          <Space>
            {canDeleteCategory && (
              <DeleteProduct
                onDeleteSuccess={reloadTable}
                name={entity.name}
                label={entity.label || ''}
              />
            )}
          </Space>
        ),
      },
    ],
    [],
  );

  const { setInitialState, initialState } = useModel('@@initialState');

  useEffect(() => {
    setInitialState({ ...initialState, collapsed: true });
  }, []);

  return (
    <>
      <UpdateProduct
        open={isModalOpen}
        onOpenChange={setModalOpen}
        onSuccess={reloadTable}
        modalProps={{
          id: currentItemId,
        }}
      />
      <ProTable<ProductItemV3>
        scroll={{ x: 960 }}
        size="small"
        actionRef={tableRef}
        rowKey="name"
        search={{ labelWidth: 'auto' }}
        request={async (params, sort, filter) => {
          const moreFilter: any = [];
          if (categoryName) {
            moreFilter.push([DOCTYPE_ERP.Item, 'item_group', '=', categoryName]);
          }
          try {
            const paramsReq = getParamsReqTable({
              doc_name: DOCTYPE_ERP.Item,
              tableReqParams: {
                params,
                sort,
                filter,
              },
              concatFilter: moreFilter,
            });
            const res = await getProductItemV3(paramsReq);
            return {
              data: res.data,
              total: res.pagination.totalElements,
            };
          } catch {
            return {
              success: false,
            };
          }
        }}
        columns={columns}
        headerTitle={<FormattedMessage id={'category.material-management.category_list'} />}
        // rowSelection={{}}
        // tableAlertOptionRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => {
        //   return <></>;
        // }}
        toolBarRender={() => toolBarRender}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
          pageSizeOptions: ['20', '50', '100'],
        }}
      />
    </>
  );
};

export default ProductsTable;
