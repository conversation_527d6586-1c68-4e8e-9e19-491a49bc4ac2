import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import { getStockReconciliationDetail } from '@/services/stock/stockReconciliation';
import { formatMoneyNumeral, formatNumeral, openInNewTab } from '@/services/utils';
import { IStockReconciliationDetail } from '@/types/warehouse.type';
import { PrinterOutlined } from '@ant-design/icons';
import {
  ActionType,
  ProColumns,
  ProForm,
  ProFormDatePicker,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { FormattedMessage, useAccess, useRequest } from '@umijs/max';
import { Button, Modal } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { FC, useEffect, useRef, useState } from 'react';
import { useStockReconciliationStore } from '../../components/ReconciliationVoucherEnhanced/stores/stockReconciliationStore';
import ActionButton from '../../components/StockActionButton';
import { voucherActionConfigs } from '../../components/StockActionButton/voucherActions';

interface Props {
  name: string;
  isModalOpen: boolean;
  setIsModalOpen: any;
  onSuccess?: () => void;
}
const ReconciliationDetail = ({ name, isModalOpen, setIsModalOpen, onSuccess }: Props) => {
  const actionRef = useRef<ActionType>();
  const store = useStockReconciliationStore();
  const [form] = useForm();
  const [items, setItems] = useState<IStockReconciliationDetail[]>([]);
  const [selectedActionComponent, setSelectedActionComponent] = useState<JSX.Element | null>(null);

  const { data, loading, refresh, run } = useRequest(getStockReconciliationDetail, {
    manual: true,
    onSuccess(data) {
      // Set data to store for ActionButton to use
      store.setSavedVoucherData(data);
    },
    onError(error) {
      console.log('error', error.message);
    },
  });

  const handleReload = () => {
    actionRef.current?.reload();
    refresh();
  };

  const access = useAccess();
  const canReadInventoryValue = access.canReadCategoryInventoryFieldLevelManagement();

  const columns: ProColumns<any>[] = [
    {
      title: <FormattedMessage id="common.index" />,
      dataIndex: 'index',
      render(dom, entity, index, action, schema) {
        return <div>{index + 1}</div>;
      },
      width: 15,
    },
    {
      title: <FormattedMessage id="warehouse-management.import-history.item_id" />,
      dataIndex: 'item_name',
      width: 10,
    },
    {
      title: <FormattedMessage id="warehouse-management.import-history.item_label" />,
      dataIndex: 'item_label',
      width: 20,
    },

    {
      title: <FormattedMessage id="common.old_quantity" />,
      dataIndex: 'current_qty',
      width: 20,
      render(dom, entity, index, action, schema) {
        return <div>{formatNumeral(entity.current_qty)}</div>;
      },
    },
    {
      title: <FormattedMessage id="common.reconciled_quantity" />,
      dataIndex: 'qty',
      width: 20,
      render(dom, entity, index, action, schema) {
        return <div>{formatNumeral(entity.qty)}</div>;
      },
    },

    {
      title: <FormattedMessage id="common.old_rate" />,
      dataIndex: 'current_valuation_rate',
      width: 20,
      render(dom, entity, index, action, schema) {
        return <div>{formatMoneyNumeral(entity.current_valuation_rate)}</div>;
      },
      hideInTable: !canReadInventoryValue,
    },
    {
      title: <FormattedMessage id="common.reconciled_rate" />,
      dataIndex: 'valuation_rate',
      width: 20,
      render(dom, entity, index, action, schema) {
        return <div>{formatMoneyNumeral(entity.valuation_rate)}</div>;
      },
      hideInTable: !canReadInventoryValue,
    },
    {
      title: <FormattedMessage id="common.unit" />,
      dataIndex: 'uom_name',
      width: 20,
    },
    {
      title: <FormattedMessage id="common.reconciled_amount" />,
      dataIndex: 'amount',
      width: 20,
      render(dom, entity, index, action, schema) {
        return <div>{formatMoneyNumeral(entity.amount)}</div>;
      },
      hideInTable: !canReadInventoryValue,
    },
  ];

  useEffect(() => {
    if (isModalOpen && name) {
      run({ name });
    }
  }, [name, isModalOpen]);

  useEffect(() => {
    if (data) {
      form.resetFields();
      form.setFieldsValue(data);
      form.setFieldValue('warehouse_label', data?.items?.at(0)?.warehouse_label);
      form.setFieldValue('user', `${data?.user_first_name} ${data?.user_last_name}`);
      setItems(data?.items || []);
    }
  }, [data]);

  // Handler for action button selection
  const handleActionSelect = (Component: FC<any>, initialData: any) => {
    setIsModalOpen(false); // Close current modal
    setSelectedActionComponent(
      <Component
        onSuccess={() => {
          onSuccess?.();
          setSelectedActionComponent(null); // Clear after success
        }}
        onClose={() => setSelectedActionComponent(null)}
        initialData={initialData}
        autoOpen={true}
      />,
    );
  };

  // Render action buttons in the footer
  const renderActionButtons = () => {
    return (
      <div
        style={{
          paddingLeft: '22rem',
          paddingRight: '22rem',

          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
          alignItems: 'center',
        }}
      >
        <Button
          key={'download'}
          icon={<PrinterOutlined />}
          onClick={() =>
            openInNewTab(`/warehouse-management-v3/to-pdf?type=reconciliation&id=${data?.name}`)
          }
        >
          {<FormattedMessage id={'common.print_receipt'} />}
        </Button>

        <ActionButton
          voucherData={data}
          actions={voucherActionConfigs['Stock Reconciliation'].map((action) => ({
            ...action,
            onSelect: () => handleActionSelect(action.createComponent, action.mapData(data)),
          }))}
          onActionSuccess={onSuccess}
          closeCurrentModal={() => {
            console.log('Closing current modal in ReconciliationDetail');
            setIsModalOpen(false);
          }}
        />
      </div>
    );
  };

  return (
    <>
      <Modal
        open={isModalOpen}
        title={<FormattedMessage id={'warehouse-management.import-history.detail'} />}
        onCancel={() => {
          setIsModalOpen(false);
          handleReload();
        }}
        footer={renderActionButtons()}
        width={1000}
      >
        <ProForm submitter={false} disabled form={form} layout="vertical" grid={true}>
          <ProFormText
            label={<FormattedMessage id={'warehouse-management.import-history.id'} />}
            colProps={{
              sm: 24,
              md: 8,
            }}
            name={'name'}
            width="md"
          />
          <ProFormDatePicker
            label={<FormattedMessage id={'warehouse-management.import-history.date'} />}
            colProps={{
              sm: 24,
              md: 8,
            }}
            name={'posting_date'}
            width="md"
            fieldProps={{
              format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
            }}
          />
          <ProFormText
            label={<FormattedMessage id={'common.assigned_to'} />}
            colProps={{
              sm: 24,
              md: 8,
            }}
            name={'user'}
            width="md"
          />
          <ProFormText
            label={<FormattedMessage id={'warehouse-management.import-history.warehouse_label'} />}
            colProps={{
              sm: 24,
              md: 8,
            }}
            name={'set_warehouse_label'}
            width="md"
          />
          <ProFormTextArea
            label={<FormattedMessage id={'common.description'} />}
            colProps={{
              sm: 24,
              md: 8,
            }}
            name={'description'}
            width="md"
          />
        </ProForm>
        <br />
        <ProTable<IStockReconciliationDetail>
          columns={columns}
          cardBordered
          size="small"
          dataSource={items}
          rowKey={'name'}
          search={false}
          loading={loading}
        />
        {/* <Row>
        <Col span={4} offset={16}>
          <FormattedMessage id="warehouse-management.import-history.total_quantity" />
        </Col>
        <Col span={4}>{formatNumeral(data?.total_qty)}</Col>
      </Row>
      <Row gutter={[0, 12]}>
        <Col span={4} offset={16}>
          <FormattedMessage id="warehouse-management.import-history.total_price" />
        </Col>
        <Col span={4}>{formatNumeral(data?.net_total)}</Col>
      </Row> */}
      </Modal>
      {selectedActionComponent}
    </>
  );
};

export default ReconciliationDetail;
