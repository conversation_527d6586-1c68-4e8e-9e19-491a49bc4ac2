import checkIcon from '@/assets/img/icons/check-circle-green.svg';
import ProCardCommon from '@/components/ProCardCommon';
import { PlusOutlined } from '@ant-design/icons';
import {
  FormListActionType,
  ProFormCheckbox,
  ProFormList,
  ProFormText
} from '@ant-design/pro-components';
import { Button, Space } from 'antd';
import { FC, ReactNode, useRef } from 'react';
interface RelatedMaterialsProps {
  children?: ReactNode;
}

const RelatedMaterials: FC<RelatedMaterialsProps> = () => {
  const actionRef = useRef<
    FormListActionType<{
      name: string;
    }>
  >();
  return (
    <ProCardCommon
      title={{
        icon: <img src={checkIcon} />,
        text: 'Vật tư liên quan',
      }}
      extra={
        <Button
          key="Add"
          icon={<PlusOutlined />}
          onClick={() => {
            actionRef.current?.add();
          }}
        >
          Thêm vật tư
        </Button>
      }
    >
      <ProFormList
        actionRef={actionRef}
        name="materials"
        initialValue={[
          {
            task: '',
            status: false,
          },
        ]}
        creatorButtonProps={false}
        deleteIconProps={{
          tooltipText: 'Xóa',
        }}
        copyIconProps={{
          tooltipText: 'Sao chép',
        }}
        alwaysShowItemLabel

      // creatorRecord={{
      //   useMode: 'none',
      // }}
      >
        {(meta, index) =>
        // Basic information of the current row {name: number; key: number}
        // meta,
        // current line number
        // index,

        // action,
        // total number of rows
        // count,
        {
          return (
            <Space align='end' key={meta.key}>

              <ProFormCheckbox
                colProps={{
                  span: '53px',
                }}
                name="status"
              />
              <ProFormText name="task" width={'sm'} label={`${index + 1}. Chọn vật tư`} />
              <ProFormText name="unix" width={'sm'} label={`${index + 1}. Đơn vị`} />
              <ProFormText
                name="ratedQuantity"
                width={'md'}
                label={`${index + 1}. Số lượng định mức`}
              />
              <ProFormText
                name="theActualNumber"
                width={'md'}
                label={`${index + 1}. Số lượng thực tế`}
              />

              {/* </ProForm.Group> */}
            </Space>
          );
        }
        }
      </ProFormList>
    </ProCardCommon>
  );
};

export default RelatedMaterials;
