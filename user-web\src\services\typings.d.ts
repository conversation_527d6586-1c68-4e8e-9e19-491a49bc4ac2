// @ts-ignore

declare namespace API {
  type ResponseResult<T> = {
    result: T;
  };
  type PaginationResponseResult<T> = {
    result: {
      data: T;
      pagination: {
        pageNumber: number;
        pageSize: number;
        totalElements: number;
        totalPages: number;
        order_by: string;
      };
    };
  };

  // Tạo type mới có thêm hasRepostingJobs
  type InventoryResponseResult<T> = {
    result: {
      data: T;
      pagination: {
        pageNumber: number;
        pageSize: number;
        totalElements: number;
        totalPages: number;
        order_by: string;
      };
      hasRepostingJobs: boolean;
    };
  };
  type ListParamsReq = {
    page?: number;
    size?: number | string;
    fields?: string[];
    filters?: any;
    or_filters?: any;
    order_by?: string;
    group_by?: string;
  };

  type InventoryV3Filters = {
    from_date?: string;
    to_date?: string;
    warehouse: string;
    item_code?: string;
  };

  type InventoryV3 = {
    iot_customer?: string;
    item_code?: string;
    item_name?: string;
    item_label?: string;
    warehouse: string;
    stock_uom?: string;
    page?: string;
    size?: string;
    order_by?: string;
  };

  type RequestLogin = {
    usr: string;
    pwd: string;
    device: string;
  };
  export interface User {
    id: string;
    address: string;
    email: string;
    image_url: string;
    name: string;
    phone: string;
    resource_id: string;
    role: string;
    status: boolean;
    username: string;
    is_deactivated: boolean;
  }

  export interface Task {
    name: string | number;
    subject?: string; // Data
    project?: string; // Link
    issue?: string; // Link
    type?: string; // Link
    color?: string; // Color
    is_group?: string; // Check
    is_template?: string; // Check
    status?:
    | 'Open'
    | 'Working'
    | 'Pending Review'
    | 'Overdue'
    | 'Template'
    | 'Scheduling'
    | 'Rescheduled'
    | 'Upcoming'
    | 'Completed'
    | 'Cancelled'
    | 'Scheduled'
    | 'Forgive'; // Open|Working|Pending Review|Overdue|Template|Scheduling|Rescheduled|Upcoming|Completed|Cancelled|Scheduled|Forgive
    priority?: 'Low' | 'Medium' | 'High' | 'Urgent'; // Low|Medium|High|Urgent
    task_weight?: number; // Float
    parent_task?: string; // Link
    completed_by?: string; // Link
    completed_on?: string; // Date
    exp_start_date?: string; // Datetime
    expected_time?: number; // Float
    start?: number; // Int
    exp_end_date?: string; // Datetime
    progress?: string; // Percent
    duration?: number; // Int
    is_milestone?: string; // Check
    description?: string; // Text Editor
    depends_on?: string; // Table
    depends_on_tasks?: string; // Code
    act_start_date?: string; // Date
    actual_time?: number; // Float
    act_end_date?: string; // Date
    total_costing_amount?: number; // Currency
    total_expense_claim?: number; // Currency
    total_billing_amount?: number; // Currency
    review_date?: string; // Date
    closing_date?: string; // Date
    department?: string; // Link
    company?: string; // Link
    lft?: number; // Int
    rgt?: number; // Int
    old_parent?: string; // Data
    teacher_id?: string; // Link
    coach_id?: string; // Link
    student_id?: string; // Link
    coaching_lesson?: string; // Link
    teaching_lesson?: string; // Link
    class_id?: string; // Link
    lesson_number?: number; // Int
    attendance?: string; // Data
    homework?: string; // Data
    language_need_to_improve?: string; // Long Text
    language_how_to_improve?: string; // Long Text
    pronun_need_to_improve?: string; // Long Text
    pronun_how_to_improve?: string; // Long Text
    feedback?: string; // Long Text
  }
}
